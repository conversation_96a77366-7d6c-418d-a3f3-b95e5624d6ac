// Chat SDK 配置文件
// 请根据您的实际情况修改以下配置

export interface ChatConfig {
  appId: string
  apiBaseUrl: string
  token: string
  userId: string
  userName: string
  userAvatar: string
}

// 默认配置 - 请替换为您的实际配置
export const defaultChatConfig: ChatConfig = {
  // Coze Agent ID - 请替换为您的实际 Agent ID
  appId: "xxx",
  
  // Coze Studio OpenAPI URL - 请替换为您的实际 API 地址
  apiBaseUrl: "http://localhost:8888",
  
  // Personal Access Token - 请替换为您的实际 PAT Token
  token: "##############",
  
  // 用户信息
  userId: "UserId123",
  userName: "Mr.XXX",
  userAvatar: "https://sf16-passport-sg.ibytedtos.com/obj/user-avatar-alisg/e0622b06d99df6ead022ca4533ca631f.png"
}

// 从环境变量获取配置（推荐用于生产环境）
export const getChatConfigFromEnv = (): Partial<ChatConfig> => {
  return {
    appId: import.meta.env.VITE_COZE_APP_ID || defaultChatConfig.appId,
    apiBaseUrl: import.meta.env.VITE_COZE_API_BASE_URL || defaultChatConfig.apiBaseUrl,
    token: import.meta.env.VITE_COZE_TOKEN || defaultChatConfig.token,
    userId: import.meta.env.VITE_USER_ID || defaultChatConfig.userId,
    userName: import.meta.env.VITE_USER_NAME || defaultChatConfig.userName,
    userAvatar: import.meta.env.VITE_USER_AVATAR || defaultChatConfig.userAvatar,
  }
}

// 获取最终配置
export const getChatConfig = (): ChatConfig => {
  const envConfig = getChatConfigFromEnv()
  return {
    ...defaultChatConfig,
    ...envConfig
  }
}
