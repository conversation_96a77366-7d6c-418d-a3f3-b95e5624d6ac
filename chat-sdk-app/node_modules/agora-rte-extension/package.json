{"name": "agora-rte-extension", "version": "1.2.4", "description": "Agora RTE Extension", "main": "./dist/agora-rte-extension.js", "typings": "./dist/agora-rte-extension.d.ts", "scripts": {"type": "api-extractor run --local --verbose", "build": "rm -rf ./lib ./dist && webpack && tsc && yarn type", "dev": "webpack --mode=development --watch", "test": "npx karma start", "wb": "rm -rf ./dist-white-brand && yarn build && wb"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.16.7", "@babel/plugin-transform-runtime": "^7.16.10", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@babel/runtime-corejs3": "^7.16.8", "@microsoft/api-extractor": "^7.19.4", "@types/chai": "^4.3.0", "@types/mocha": "^9.1.0", "babel-loader": "^8.2.3", "chai": "^4.3.6", "core-js": "3", "karma": "^6.3.16", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-typescript": "^5.5.3", "mocha": "^9.2.0", "typescript": "^4.5.4", "webpack": "^5.65.0", "webpack-cli": "^4.9.1", "white-brand-cli": "^1.2.3"}, "files": ["dist", "CHANGELOG.md"], "browserslist": ["chrome >= 58", "ios >= 11", "safari >= 11", "firefox >= 56"]}