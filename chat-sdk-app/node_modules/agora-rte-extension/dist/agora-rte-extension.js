!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.AgoraRTEExtension=e():t.AgoraRTEExtension=e()}(self,(()=>(()=>{var t={6226:(t,e,r)=>{t.exports=r(2948)},4437:(t,e,r)=>{"use strict";var n=r(5602);t.exports=n},204:(t,e,r)=>{"use strict";var n=r(4454);r(3705),r(1935),r(1944),r(5539),t.exports=n},5003:(t,e,r)=>{"use strict";var n=r(4690);t.exports=n},5887:(t,e,r)=>{"use strict";var n=r(7263);t.exports=n},8641:(t,e,r)=>{"use strict";r(9727);var n=r(9068).Object,o=t.exports=function(t,e,r){return n.defineProperty(t,e,r)};n.defineProperty.sham&&(o.sham=!0)},8462:(t,e,r)=>{"use strict";r(6864),r(1997),r(6069),r(9927),r(8795),r(3233),r(8840),r(6028),r(1345);var n=r(9068);t.exports=n.Promise},3941:(t,e,r)=>{"use strict";r(1845),r(6069),r(1967),r(342),r(8861),r(3092),r(6538),r(459),r(2303),r(3236),r(1654),r(4833),r(316),r(6925),r(3135),r(9390),r(5938),r(8518),r(9786),r(6716);var n=r(9068);t.exports=n.Symbol},4101:(t,e,r)=>{"use strict";r(1997),r(6069),r(1345),r(459);var n=r(8248);t.exports=n.f("iterator")},7548:(t,e,r)=>{"use strict";r(8242),r(3135);var n=r(8248);t.exports=n.f("toPrimitive")},1685:(t,e,r)=>{"use strict";var n=r(4437);t.exports=n},1768:(t,e,r)=>{"use strict";var n=r(204);r(3361),r(1823),r(4163),r(6499),r(6714),r(5704),r(6206),r(1548),r(1666),t.exports=n},6228:(t,e,r)=>{"use strict";var n=r(5003);t.exports=n},4360:(t,e,r)=>{"use strict";var n=r(5887);t.exports=n},5935:(t,e,r)=>{"use strict";var n=r(9934),o=r(1028),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a function")}},3164:(t,e,r)=>{"use strict";var n=r(7936),o=r(1028),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a constructor")}},7844:(t,e,r)=>{"use strict";var n=r(9934),o=String,i=TypeError;t.exports=function(t){if("object"==typeof t||n(t))return t;throw new i("Can't set "+o(t)+" as a prototype")}},6888:t=>{"use strict";t.exports=function(){}},927:(t,e,r)=>{"use strict";var n=r(1727),o=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw new o("Incorrect invocation")}},8879:(t,e,r)=>{"use strict";var n=r(9611),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not an object")}},8520:(t,e,r)=>{"use strict";var n=r(3747),o=r(8100),i=r(7165),s=function(t){return function(e,r,s){var c,u=n(e),a=i(u),f=o(s,a);if(t&&r!=r){for(;a>f;)if((c=u[f++])!=c)return!0}else for(;a>f;f++)if((t||f in u)&&u[f]===r)return t||f||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},2503:(t,e,r)=>{"use strict";var n=r(9605),o=r(2537),i=r(108),s=r(2962),c=r(7165),u=r(7265),a=o([].push),f=function(t){var e=1===t,r=2===t,o=3===t,f=4===t,p=6===t,l=7===t,v=5===t||p;return function(h,d,y,g){for(var m,b,x=s(h),w=i(x),S=c(w),O=n(d,y),T=0,j=g||u,P=e?j(h,S):r||l?j(h,0):void 0;S>T;T++)if((v||T in w)&&(b=O(m=w[T],T,x),t))if(e)P[T]=b;else if(b)switch(t){case 3:return!0;case 5:return m;case 6:return T;case 2:a(P,m)}else switch(t){case 4:return!1;case 7:a(P,m)}return p?-1:o||f?f:P}};t.exports={forEach:f(0),map:f(1),filter:f(2),some:f(3),every:f(4),find:f(5),findIndex:f(6),filterReject:f(7)}},8388:(t,e,r)=>{"use strict";var n=r(9353),o=r(2442),i=r(5131),s=o("species");t.exports=function(t){return i>=51||!n((function(){var e=[];return(e.constructor={})[s]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},4030:(t,e,r)=>{"use strict";var n=r(8100),o=r(7165),i=r(981),s=Array,c=Math.max;t.exports=function(t,e,r){for(var u=o(t),a=n(e,u),f=n(void 0===r?u:r,u),p=s(c(f-a,0)),l=0;a<f;a++,l++)i(p,l,t[a]);return p.length=l,p}},2076:(t,e,r)=>{"use strict";var n=r(2537);t.exports=n([].slice)},1388:(t,e,r)=>{"use strict";var n=r(3527),o=r(7936),i=r(9611),s=r(2442)("species"),c=Array;t.exports=function(t){var e;return n(t)&&(e=t.constructor,(o(e)&&(e===c||n(e.prototype))||i(e)&&null===(e=e[s]))&&(e=void 0)),void 0===e?c:e}},7265:(t,e,r)=>{"use strict";var n=r(1388);t.exports=function(t,e){return new(n(t))(0===e?0:e)}},7670:(t,e,r)=>{"use strict";var n=r(2442)("iterator"),o=!1;try{var i=0,s={next:function(){return{done:!!i++}},return:function(){o=!0}};s[n]=function(){return this},Array.from(s,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!o)return!1}catch(t){return!1}var r=!1;try{var i={};i[n]=function(){return{next:function(){return{done:r=!0}}}},t(i)}catch(t){}return r}},4650:(t,e,r)=>{"use strict";var n=r(2537),o=n({}.toString),i=n("".slice);t.exports=function(t){return i(o(t),8,-1)}},6397:(t,e,r)=>{"use strict";var n=r(3220),o=r(9934),i=r(4650),s=r(2442)("toStringTag"),c=Object,u="Arguments"===i(function(){return arguments}());t.exports=n?i:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=c(t),s))?r:u?i(e):"Object"===(n=i(e))&&o(e.callee)?"Arguments":n}},5895:(t,e,r)=>{"use strict";var n=r(9027),o=r(704),i=r(5396),s=r(1890);t.exports=function(t,e,r){for(var c=o(e),u=s.f,a=i.f,f=0;f<c.length;f++){var p=c[f];n(t,p)||r&&n(r,p)||u(t,p,a(e,p))}}},4853:(t,e,r)=>{"use strict";var n=r(9353);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},7474:t=>{"use strict";t.exports=function(t,e){return{value:t,done:e}}},7151:(t,e,r)=>{"use strict";var n=r(3794),o=r(1890),i=r(1567);t.exports=n?function(t,e,r){return o.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},1567:t=>{"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},981:(t,e,r)=>{"use strict";var n=r(1525),o=r(1890),i=r(1567);t.exports=function(t,e,r){var s=n(e);s in t?o.f(t,s,i(0,r)):t[s]=r}},3089:(t,e,r)=>{"use strict";var n=r(1890);t.exports=function(t,e,r){return n.f(t,e,r)}},1733:(t,e,r)=>{"use strict";var n=r(7151);t.exports=function(t,e,r,o){return o&&o.enumerable?t[e]=r:n(t,e,r),t}},543:(t,e,r)=>{"use strict";var n=r(5685),o=Object.defineProperty;t.exports=function(t,e){try{o(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},3794:(t,e,r)=>{"use strict";var n=r(9353);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},9945:t=>{"use strict";var e="object"==typeof document&&document.all,r=void 0===e&&void 0!==e;t.exports={all:e,IS_HTMLDDA:r}},3729:(t,e,r)=>{"use strict";var n=r(5685),o=r(9611),i=n.document,s=o(i)&&o(i.createElement);t.exports=function(t){return s?i.createElement(t):{}}},9939:t=>{"use strict";var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},8920:t=>{"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},3:(t,e,r)=>{"use strict";var n=r(9207),o=r(4408);t.exports=!n&&!o&&"object"==typeof window&&"object"==typeof document},9207:t=>{"use strict";t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},8309:(t,e,r)=>{"use strict";var n=r(3642);t.exports=/ipad|iphone|ipod/i.test(n)&&"undefined"!=typeof Pebble},8816:(t,e,r)=>{"use strict";var n=r(3642);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},4408:(t,e,r)=>{"use strict";var n=r(5685),o=r(4650);t.exports="process"===o(n.process)},9267:(t,e,r)=>{"use strict";var n=r(3642);t.exports=/web0s(?!.*chrome)/i.test(n)},3642:t=>{"use strict";t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},5131:(t,e,r)=>{"use strict";var n,o,i=r(5685),s=r(3642),c=i.process,u=i.Deno,a=c&&c.versions||u&&u.version,f=a&&a.v8;f&&(o=(n=f.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&s&&(!(n=s.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=s.match(/Chrome\/(\d+)/))&&(o=+n[1]),t.exports=o},270:t=>{"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},7918:(t,e,r)=>{"use strict";var n=r(2537),o=Error,i=n("".replace),s=String(new o("zxcasd").stack),c=/\n\s*at [^:]*:[^\n]*/,u=c.test(s);t.exports=function(t,e){if(u&&"string"==typeof t&&!o.prepareStackTrace)for(;e--;)t=i(t,c,"");return t}},1794:(t,e,r)=>{"use strict";var n=r(7151),o=r(7918),i=r(4671),s=Error.captureStackTrace;t.exports=function(t,e,r,c){i&&(s?s(t,e):n(t,"stack",o(r,c)))}},4671:(t,e,r)=>{"use strict";var n=r(9353),o=r(1567);t.exports=!n((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",o(1,7)),7!==t.stack)}))},4715:(t,e,r)=>{"use strict";var n=r(5685),o=r(145),i=r(7531),s=r(9934),c=r(5396).f,u=r(5703),a=r(9068),f=r(9605),p=r(7151),l=r(9027),v=function(t){var e=function(r,n,i){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(r);case 2:return new t(r,n)}return new t(r,n,i)}return o(t,this,arguments)};return e.prototype=t.prototype,e};t.exports=function(t,e){var r,o,h,d,y,g,m,b,x,w=t.target,S=t.global,O=t.stat,T=t.proto,j=S?n:O?n[w]:(n[w]||{}).prototype,P=S?a:a[w]||p(a,w,{})[w],E=P.prototype;for(d in e)o=!(r=u(S?d:w+(O?".":"#")+d,t.forced))&&j&&l(j,d),g=P[d],o&&(m=t.dontCallGetSet?(x=c(j,d))&&x.value:j[d]),y=o&&m?m:e[d],o&&typeof g==typeof y||(b=t.bind&&o?f(y,n):t.wrap&&o?v(y):T&&s(y)?i(y):y,(t.sham||y&&y.sham||g&&g.sham)&&p(b,"sham",!0),p(P,d,b),T&&(l(a,h=w+"Prototype")||p(a,h,{}),p(a[h],d,y),t.real&&E&&(r||!E[d])&&p(E,d,y)))}},9353:t=>{"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},145:(t,e,r)=>{"use strict";var n=r(6229),o=Function.prototype,i=o.apply,s=o.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?s.bind(i):function(){return s.apply(i,arguments)})},9605:(t,e,r)=>{"use strict";var n=r(7531),o=r(5935),i=r(6229),s=n(n.bind);t.exports=function(t,e){return o(t),void 0===e?t:i?s(t,e):function(){return t.apply(e,arguments)}}},6229:(t,e,r)=>{"use strict";var n=r(9353);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},3417:(t,e,r)=>{"use strict";var n=r(6229),o=Function.prototype.call;t.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},8766:(t,e,r)=>{"use strict";var n=r(3794),o=r(9027),i=Function.prototype,s=n&&Object.getOwnPropertyDescriptor,c=o(i,"name"),u=c&&"something"===function(){}.name,a=c&&(!n||n&&s(i,"name").configurable);t.exports={EXISTS:c,PROPER:u,CONFIGURABLE:a}},7665:(t,e,r)=>{"use strict";var n=r(2537),o=r(5935);t.exports=function(t,e,r){try{return n(o(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},7531:(t,e,r)=>{"use strict";var n=r(4650),o=r(2537);t.exports=function(t){if("Function"===n(t))return o(t)}},2537:(t,e,r)=>{"use strict";var n=r(6229),o=Function.prototype,i=o.call,s=n&&o.bind.bind(i,i);t.exports=n?s:function(t){return function(){return i.apply(t,arguments)}}},7192:(t,e,r)=>{"use strict";var n=r(9068),o=r(5685),i=r(9934),s=function(t){return i(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?s(n[t])||s(o[t]):n[t]&&n[t][e]||o[t]&&o[t][e]}},610:(t,e,r)=>{"use strict";var n=r(6397),o=r(5752),i=r(4133),s=r(9234),c=r(2442)("iterator");t.exports=function(t){if(!i(t))return o(t,c)||o(t,"@@iterator")||s[n(t)]}},3029:(t,e,r)=>{"use strict";var n=r(3417),o=r(5935),i=r(8879),s=r(1028),c=r(610),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?c(t):e;if(o(r))return i(n(r,t));throw new u(s(t)+" is not iterable")}},9647:(t,e,r)=>{"use strict";var n=r(2537),o=r(3527),i=r(9934),s=r(4650),c=r(1182),u=n([].push);t.exports=function(t){if(i(t))return t;if(o(t)){for(var e=t.length,r=[],n=0;n<e;n++){var a=t[n];"string"==typeof a?u(r,a):"number"!=typeof a&&"Number"!==s(a)&&"String"!==s(a)||u(r,c(a))}var f=r.length,p=!0;return function(t,e){if(p)return p=!1,e;if(o(this))return e;for(var n=0;n<f;n++)if(r[n]===t)return e}}}},5752:(t,e,r)=>{"use strict";var n=r(5935),o=r(4133);t.exports=function(t,e){var r=t[e];return o(r)?void 0:n(r)}},5685:function(t,e,r){"use strict";var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9027:(t,e,r)=>{"use strict";var n=r(2537),o=r(2962),i=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return i(o(t),e)}},9775:t=>{"use strict";t.exports={}},2210:t=>{"use strict";t.exports=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}}},6395:(t,e,r)=>{"use strict";var n=r(7192);t.exports=n("document","documentElement")},9548:(t,e,r)=>{"use strict";var n=r(3794),o=r(9353),i=r(3729);t.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},108:(t,e,r)=>{"use strict";var n=r(2537),o=r(9353),i=r(4650),s=Object,c=n("".split);t.exports=o((function(){return!s("z").propertyIsEnumerable(0)}))?function(t){return"String"===i(t)?c(t,""):s(t)}:s},3698:(t,e,r)=>{"use strict";var n=r(2537),o=r(9934),i=r(5509),s=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(t){return s(t)}),t.exports=i.inspectSource},2071:(t,e,r)=>{"use strict";var n=r(9611),o=r(7151);t.exports=function(t,e){n(e)&&"cause"in e&&o(t,"cause",e.cause)}},4084:(t,e,r)=>{"use strict";var n,o,i,s=r(9033),c=r(5685),u=r(9611),a=r(7151),f=r(9027),p=r(5509),l=r(3287),v=r(9775),h="Object already initialized",d=c.TypeError,y=c.WeakMap;if(s||p.state){var g=p.state||(p.state=new y);g.get=g.get,g.has=g.has,g.set=g.set,n=function(t,e){if(g.has(t))throw new d(h);return e.facade=t,g.set(t,e),e},o=function(t){return g.get(t)||{}},i=function(t){return g.has(t)}}else{var m=l("state");v[m]=!0,n=function(t,e){if(f(t,m))throw new d(h);return e.facade=t,a(t,m,e),e},o=function(t){return f(t,m)?t[m]:{}},i=function(t){return f(t,m)}}t.exports={set:n,get:o,has:i,enforce:function(t){return i(t)?o(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=o(e)).type!==t)throw new d("Incompatible receiver, "+t+" required");return r}}}},9273:(t,e,r)=>{"use strict";var n=r(2442),o=r(9234),i=n("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||s[i]===t)}},3527:(t,e,r)=>{"use strict";var n=r(4650);t.exports=Array.isArray||function(t){return"Array"===n(t)}},9934:(t,e,r)=>{"use strict";var n=r(9945),o=n.all;t.exports=n.IS_HTMLDDA?function(t){return"function"==typeof t||t===o}:function(t){return"function"==typeof t}},7936:(t,e,r)=>{"use strict";var n=r(2537),o=r(9353),i=r(9934),s=r(6397),c=r(7192),u=r(3698),a=function(){},f=[],p=c("Reflect","construct"),l=/^\s*(?:class|function)\b/,v=n(l.exec),h=!l.test(a),d=function(t){if(!i(t))return!1;try{return p(a,f,t),!0}catch(t){return!1}},y=function(t){if(!i(t))return!1;switch(s(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return h||!!v(l,u(t))}catch(t){return!0}};y.sham=!0,t.exports=!p||o((function(){var t;return d(d.call)||!d(Object)||!d((function(){t=!0}))||t}))?y:d},5703:(t,e,r)=>{"use strict";var n=r(9353),o=r(9934),i=/#|\.prototype\./,s=function(t,e){var r=u[c(t)];return r===f||r!==a&&(o(e)?n(e):!!e)},c=s.normalize=function(t){return String(t).replace(i,".").toLowerCase()},u=s.data={},a=s.NATIVE="N",f=s.POLYFILL="P";t.exports=s},4133:t=>{"use strict";t.exports=function(t){return null==t}},9611:(t,e,r)=>{"use strict";var n=r(9934),o=r(9945),i=o.all;t.exports=o.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:n(t)||t===i}:function(t){return"object"==typeof t?null!==t:n(t)}},4081:t=>{"use strict";t.exports=!0},205:(t,e,r)=>{"use strict";var n=r(7192),o=r(9934),i=r(1727),s=r(16),c=Object;t.exports=s?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return o(e)&&i(e.prototype,c(t))}},9614:(t,e,r)=>{"use strict";var n=r(9605),o=r(3417),i=r(8879),s=r(1028),c=r(9273),u=r(7165),a=r(1727),f=r(3029),p=r(610),l=r(273),v=TypeError,h=function(t,e){this.stopped=t,this.result=e},d=h.prototype;t.exports=function(t,e,r){var y,g,m,b,x,w,S,O=r&&r.that,T=!(!r||!r.AS_ENTRIES),j=!(!r||!r.IS_RECORD),P=!(!r||!r.IS_ITERATOR),E=!(!r||!r.INTERRUPTED),k=n(e,O),_=function(t){return y&&l(y,"normal",t),new h(!0,t)},A=function(t){return T?(i(t),E?k(t[0],t[1],_):k(t[0],t[1])):E?k(t,_):k(t)};if(j)y=t.iterator;else if(P)y=t;else{if(!(g=p(t)))throw new v(s(t)+" is not iterable");if(c(g)){for(m=0,b=u(t);b>m;m++)if((x=A(t[m]))&&a(d,x))return x;return new h(!1)}y=f(t,g)}for(w=j?t.next:y.next;!(S=o(w,y)).done;){try{x=A(S.value)}catch(t){l(y,"throw",t)}if("object"==typeof x&&x&&a(d,x))return x}return new h(!1)}},273:(t,e,r)=>{"use strict";var n=r(3417),o=r(8879),i=r(5752);t.exports=function(t,e,r){var s,c;o(t);try{if(!(s=i(t,"return"))){if("throw"===e)throw r;return r}s=n(s,t)}catch(t){c=!0,s=t}if("throw"===e)throw r;if(c)throw s;return o(s),r}},4406:(t,e,r)=>{"use strict";var n=r(8176).IteratorPrototype,o=r(3010),i=r(1567),s=r(4196),c=r(9234),u=function(){return this};t.exports=function(t,e,r,a){var f=e+" Iterator";return t.prototype=o(n,{next:i(+!a,r)}),s(t,f,!1,!0),c[f]=u,t}},6483:(t,e,r)=>{"use strict";var n=r(4715),o=r(3417),i=r(4081),s=r(8766),c=r(9934),u=r(4406),a=r(3863),f=r(1350),p=r(4196),l=r(7151),v=r(1733),h=r(2442),d=r(9234),y=r(8176),g=s.PROPER,m=s.CONFIGURABLE,b=y.IteratorPrototype,x=y.BUGGY_SAFARI_ITERATORS,w=h("iterator"),S="keys",O="values",T="entries",j=function(){return this};t.exports=function(t,e,r,s,h,y,P){u(r,e,s);var E,k,_,A=function(t){if(t===h&&D)return D;if(!x&&t&&t in C)return C[t];switch(t){case S:case O:case T:return function(){return new r(this,t)}}return function(){return new r(this)}},I=e+" Iterator",R=!1,C=t.prototype,L=C[w]||C["@@iterator"]||h&&C[h],D=!x&&L||A(h),M="Array"===e&&C.entries||L;if(M&&(E=a(M.call(new t)))!==Object.prototype&&E.next&&(i||a(E)===b||(f?f(E,b):c(E[w])||v(E,w,j)),p(E,I,!0,!0),i&&(d[I]=j)),g&&h===O&&L&&L.name!==O&&(!i&&m?l(C,"name",O):(R=!0,D=function(){return o(L,this)})),h)if(k={values:A(O),keys:y?D:A(S),entries:A(T)},P)for(_ in k)(x||R||!(_ in C))&&v(C,_,k[_]);else n({target:e,proto:!0,forced:x||R},k);return i&&!P||C[w]===D||v(C,w,D,{name:h}),d[e]=D,k}},8176:(t,e,r)=>{"use strict";var n,o,i,s=r(9353),c=r(9934),u=r(9611),a=r(3010),f=r(3863),p=r(1733),l=r(2442),v=r(4081),h=l("iterator"),d=!1;[].keys&&("next"in(i=[].keys())?(o=f(f(i)))!==Object.prototype&&(n=o):d=!0),!u(n)||s((function(){var t={};return n[h].call(t)!==t}))?n={}:v&&(n=a(n)),c(n[h])||p(n,h,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:d}},9234:t=>{"use strict";t.exports={}},7165:(t,e,r)=>{"use strict";var n=r(1904);t.exports=function(t){return n(t.length)}},8836:t=>{"use strict";var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)}},5996:(t,e,r)=>{"use strict";var n,o,i,s,c,u=r(5685),a=r(9605),f=r(5396).f,p=r(6727).set,l=r(5721),v=r(8816),h=r(8309),d=r(9267),y=r(4408),g=u.MutationObserver||u.WebKitMutationObserver,m=u.document,b=u.process,x=u.Promise,w=f(u,"queueMicrotask"),S=w&&w.value;if(!S){var O=new l,T=function(){var t,e;for(y&&(t=b.domain)&&t.exit();e=O.get();)try{e()}catch(t){throw O.head&&n(),t}t&&t.enter()};v||y||d||!g||!m?!h&&x&&x.resolve?((s=x.resolve(void 0)).constructor=x,c=a(s.then,s),n=function(){c(T)}):y?n=function(){b.nextTick(T)}:(p=a(p,u),n=function(){p(T)}):(o=!0,i=m.createTextNode(""),new g(T).observe(i,{characterData:!0}),n=function(){i.data=o=!o}),S=function(t){O.head||n(),O.add(t)}}t.exports=S},2157:(t,e,r)=>{"use strict";var n=r(5935),o=TypeError,i=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw new o("Bad Promise constructor");e=t,r=n})),this.resolve=n(e),this.reject=n(r)};t.exports.f=function(t){return new i(t)}},81:(t,e,r)=>{"use strict";var n=r(1182);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},3010:(t,e,r)=>{"use strict";var n,o=r(8879),i=r(7832),s=r(270),c=r(9775),u=r(6395),a=r(3729),f=r(3287),p="prototype",l="script",v=f("IE_PROTO"),h=function(){},d=function(t){return"<"+l+">"+t+"</"+l+">"},y=function(t){t.write(d("")),t.close();var e=t.parentWindow.Object;return t=null,e},g=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;g="undefined"!=typeof document?document.domain&&n?y(n):(e=a("iframe"),r="java"+l+":",e.style.display="none",u.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(d("document.F=Object")),t.close(),t.F):y(n);for(var o=s.length;o--;)delete g[p][s[o]];return g()};c[v]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(h[p]=o(t),r=new h,h[p]=null,r[v]=t):r=g(),void 0===e?r:i.f(r,e)}},7832:(t,e,r)=>{"use strict";var n=r(3794),o=r(7956),i=r(1890),s=r(8879),c=r(3747),u=r(7508);e.f=n&&!o?Object.defineProperties:function(t,e){s(t);for(var r,n=c(e),o=u(e),a=o.length,f=0;a>f;)i.f(t,r=o[f++],n[r]);return t}},1890:(t,e,r)=>{"use strict";var n=r(3794),o=r(9548),i=r(7956),s=r(8879),c=r(1525),u=TypeError,a=Object.defineProperty,f=Object.getOwnPropertyDescriptor,p="enumerable",l="configurable",v="writable";e.f=n?i?function(t,e,r){if(s(t),e=c(e),s(r),"function"==typeof t&&"prototype"===e&&"value"in r&&v in r&&!r[v]){var n=f(t,e);n&&n[v]&&(t[e]=r.value,r={configurable:l in r?r[l]:n[l],enumerable:p in r?r[p]:n[p],writable:!1})}return a(t,e,r)}:a:function(t,e,r){if(s(t),e=c(e),s(r),o)try{return a(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},5396:(t,e,r)=>{"use strict";var n=r(3794),o=r(3417),i=r(9106),s=r(1567),c=r(3747),u=r(1525),a=r(9027),f=r(9548),p=Object.getOwnPropertyDescriptor;e.f=n?p:function(t,e){if(t=c(t),e=u(e),f)try{return p(t,e)}catch(t){}if(a(t,e))return s(!o(i.f,t,e),t[e])}},7195:(t,e,r)=>{"use strict";var n=r(4650),o=r(3747),i=r(4582).f,s=r(4030),c="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return c&&"Window"===n(t)?function(t){try{return i(t)}catch(t){return s(c)}}(t):i(o(t))}},4582:(t,e,r)=>{"use strict";var n=r(97),o=r(270).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,o)}},6953:(t,e)=>{"use strict";e.f=Object.getOwnPropertySymbols},3863:(t,e,r)=>{"use strict";var n=r(9027),o=r(9934),i=r(2962),s=r(3287),c=r(4853),u=s("IE_PROTO"),a=Object,f=a.prototype;t.exports=c?a.getPrototypeOf:function(t){var e=i(t);if(n(e,u))return e[u];var r=e.constructor;return o(r)&&e instanceof r?r.prototype:e instanceof a?f:null}},1727:(t,e,r)=>{"use strict";var n=r(2537);t.exports=n({}.isPrototypeOf)},97:(t,e,r)=>{"use strict";var n=r(2537),o=r(9027),i=r(3747),s=r(8520).indexOf,c=r(9775),u=n([].push);t.exports=function(t,e){var r,n=i(t),a=0,f=[];for(r in n)!o(c,r)&&o(n,r)&&u(f,r);for(;e.length>a;)o(n,r=e[a++])&&(~s(f,r)||u(f,r));return f}},7508:(t,e,r)=>{"use strict";var n=r(97),o=r(270);t.exports=Object.keys||function(t){return n(t,o)}},9106:(t,e)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);e.f=o?function(t){var e=n(this,t);return!!e&&e.enumerable}:r},1350:(t,e,r)=>{"use strict";var n=r(7665),o=r(8879),i=r(7844);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=n(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return o(r),i(n),e?t(r,n):r.__proto__=n,r}}():void 0)},8516:(t,e,r)=>{"use strict";var n=r(3220),o=r(6397);t.exports=n?{}.toString:function(){return"[object "+o(this)+"]"}},8733:(t,e,r)=>{"use strict";var n=r(3417),o=r(9934),i=r(9611),s=TypeError;t.exports=function(t,e){var r,c;if("string"===e&&o(r=t.toString)&&!i(c=n(r,t)))return c;if(o(r=t.valueOf)&&!i(c=n(r,t)))return c;if("string"!==e&&o(r=t.toString)&&!i(c=n(r,t)))return c;throw new s("Can't convert object to primitive value")}},704:(t,e,r)=>{"use strict";var n=r(7192),o=r(2537),i=r(4582),s=r(6953),c=r(8879),u=o([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=i.f(c(t)),r=s.f;return r?u(e,r(t)):e}},9068:t=>{"use strict";t.exports={}},3183:t=>{"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},4865:(t,e,r)=>{"use strict";var n=r(5685),o=r(3159),i=r(9934),s=r(5703),c=r(3698),u=r(2442),a=r(3),f=r(9207),p=r(4081),l=r(5131),v=o&&o.prototype,h=u("species"),d=!1,y=i(n.PromiseRejectionEvent),g=s("Promise",(function(){var t=c(o),e=t!==String(o);if(!e&&66===l)return!0;if(p&&(!v.catch||!v.finally))return!0;if(!l||l<51||!/native code/.test(t)){var r=new o((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((r.constructor={})[h]=n,!(d=r.then((function(){}))instanceof n))return!0}return!e&&(a||f)&&!y}));t.exports={CONSTRUCTOR:g,REJECTION_EVENT:y,SUBCLASSING:d}},3159:(t,e,r)=>{"use strict";var n=r(5685);t.exports=n.Promise},5712:(t,e,r)=>{"use strict";var n=r(8879),o=r(9611),i=r(2157);t.exports=function(t,e){if(n(t),o(e)&&e.constructor===t)return e;var r=i.f(t);return(0,r.resolve)(e),r.promise}},7290:(t,e,r)=>{"use strict";var n=r(3159),o=r(7670),i=r(4865).CONSTRUCTOR;t.exports=i||!o((function(t){n.all(t).then(void 0,(function(){}))}))},5721:t=>{"use strict";var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=e},9823:(t,e,r)=>{"use strict";var n=r(4133),o=TypeError;t.exports=function(t){if(n(t))throw new o("Can't call method on "+t);return t}},4799:(t,e,r)=>{"use strict";var n=r(7192),o=r(3089),i=r(2442),s=r(3794),c=i("species");t.exports=function(t){var e=n(t);s&&e&&!e[c]&&o(e,c,{configurable:!0,get:function(){return this}})}},4196:(t,e,r)=>{"use strict";var n=r(3220),o=r(1890).f,i=r(7151),s=r(9027),c=r(8516),u=r(2442)("toStringTag");t.exports=function(t,e,r,a){var f=r?t:t&&t.prototype;f&&(s(f,u)||o(f,u,{configurable:!0,value:e}),a&&!n&&i(f,"toString",c))}},3287:(t,e,r)=>{"use strict";var n=r(3921),o=r(3440),i=n("keys");t.exports=function(t){return i[t]||(i[t]=o(t))}},5509:(t,e,r)=>{"use strict";var n=r(5685),o=r(543),i="__core-js_shared__",s=n[i]||o(i,{});t.exports=s},3921:(t,e,r)=>{"use strict";var n=r(4081),o=r(5509);(t.exports=function(t,e){return o[t]||(o[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.34.0",mode:n?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.34.0/LICENSE",source:"https://github.com/zloirock/core-js"})},9022:(t,e,r)=>{"use strict";var n=r(8879),o=r(3164),i=r(4133),s=r(2442)("species");t.exports=function(t,e){var r,c=n(t).constructor;return void 0===c||i(r=n(c)[s])?e:o(r)}},5202:(t,e,r)=>{"use strict";var n=r(2537),o=r(6169),i=r(1182),s=r(9823),c=n("".charAt),u=n("".charCodeAt),a=n("".slice),f=function(t){return function(e,r){var n,f,p=i(s(e)),l=o(r),v=p.length;return l<0||l>=v?t?"":void 0:(n=u(p,l))<55296||n>56319||l+1===v||(f=u(p,l+1))<56320||f>57343?t?c(p,l):n:t?a(p,l,l+2):f-56320+(n-55296<<10)+65536}};t.exports={codeAt:f(!1),charAt:f(!0)}},4086:(t,e,r)=>{"use strict";var n=r(5131),o=r(9353),i=r(5685).String;t.exports=!!Object.getOwnPropertySymbols&&!o((function(){var t=Symbol("symbol detection");return!i(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},9681:(t,e,r)=>{"use strict";var n=r(3417),o=r(7192),i=r(2442),s=r(1733);t.exports=function(){var t=o("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,c=i("toPrimitive");e&&!e[c]&&s(e,c,(function(t){return n(r,this)}),{arity:1})}},3203:(t,e,r)=>{"use strict";var n=r(7192),o=r(2537),i=n("Symbol"),s=i.keyFor,c=o(i.prototype.valueOf);t.exports=i.isRegisteredSymbol||function(t){try{return void 0!==s(c(t))}catch(t){return!1}}},9003:(t,e,r)=>{"use strict";for(var n=r(3921),o=r(7192),i=r(2537),s=r(205),c=r(2442),u=o("Symbol"),a=u.isWellKnownSymbol,f=o("Object","getOwnPropertyNames"),p=i(u.prototype.valueOf),l=n("wks"),v=0,h=f(u),d=h.length;v<d;v++)try{var y=h[v];s(u[y])&&c(y)}catch(t){}t.exports=function(t){if(a&&a(t))return!0;try{for(var e=p(t),r=0,n=f(l),o=n.length;r<o;r++)if(l[n[r]]==e)return!0}catch(t){}return!1}},5731:(t,e,r)=>{"use strict";var n=r(4086);t.exports=n&&!!Symbol.for&&!!Symbol.keyFor},6727:(t,e,r)=>{"use strict";var n,o,i,s,c=r(5685),u=r(145),a=r(9605),f=r(9934),p=r(9027),l=r(9353),v=r(6395),h=r(2076),d=r(3729),y=r(2891),g=r(8816),m=r(4408),b=c.setImmediate,x=c.clearImmediate,w=c.process,S=c.Dispatch,O=c.Function,T=c.MessageChannel,j=c.String,P=0,E={},k="onreadystatechange";l((function(){n=c.location}));var _=function(t){if(p(E,t)){var e=E[t];delete E[t],e()}},A=function(t){return function(){_(t)}},I=function(t){_(t.data)},R=function(t){c.postMessage(j(t),n.protocol+"//"+n.host)};b&&x||(b=function(t){y(arguments.length,1);var e=f(t)?t:O(t),r=h(arguments,1);return E[++P]=function(){u(e,void 0,r)},o(P),P},x=function(t){delete E[t]},m?o=function(t){w.nextTick(A(t))}:S&&S.now?o=function(t){S.now(A(t))}:T&&!g?(s=(i=new T).port2,i.port1.onmessage=I,o=a(s.postMessage,s)):c.addEventListener&&f(c.postMessage)&&!c.importScripts&&n&&"file:"!==n.protocol&&!l(R)?(o=R,c.addEventListener("message",I,!1)):o=k in d("script")?function(t){v.appendChild(d("script"))[k]=function(){v.removeChild(this),_(t)}}:function(t){setTimeout(A(t),0)}),t.exports={set:b,clear:x}},8100:(t,e,r)=>{"use strict";var n=r(6169),o=Math.max,i=Math.min;t.exports=function(t,e){var r=n(t);return r<0?o(r+e,0):i(r,e)}},3747:(t,e,r)=>{"use strict";var n=r(108),o=r(9823);t.exports=function(t){return n(o(t))}},6169:(t,e,r)=>{"use strict";var n=r(8836);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},1904:(t,e,r)=>{"use strict";var n=r(6169),o=Math.min;t.exports=function(t){return t>0?o(n(t),9007199254740991):0}},2962:(t,e,r)=>{"use strict";var n=r(9823),o=Object;t.exports=function(t){return o(n(t))}},681:(t,e,r)=>{"use strict";var n=r(3417),o=r(9611),i=r(205),s=r(5752),c=r(8733),u=r(2442),a=TypeError,f=u("toPrimitive");t.exports=function(t,e){if(!o(t)||i(t))return t;var r,u=s(t,f);if(u){if(void 0===e&&(e="default"),r=n(u,t,e),!o(r)||i(r))return r;throw new a("Can't convert object to primitive value")}return void 0===e&&(e="number"),c(t,e)}},1525:(t,e,r)=>{"use strict";var n=r(681),o=r(205);t.exports=function(t){var e=n(t,"string");return o(e)?e:e+""}},3220:(t,e,r)=>{"use strict";var n={};n[r(2442)("toStringTag")]="z",t.exports="[object z]"===String(n)},1182:(t,e,r)=>{"use strict";var n=r(6397),o=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return o(t)}},1028:t=>{"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},3440:(t,e,r)=>{"use strict";var n=r(2537),o=0,i=Math.random(),s=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+s(++o+i,36)}},16:(t,e,r)=>{"use strict";var n=r(4086);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7956:(t,e,r)=>{"use strict";var n=r(3794),o=r(9353);t.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2891:t=>{"use strict";var e=TypeError;t.exports=function(t,r){if(t<r)throw new e("Not enough arguments");return t}},9033:(t,e,r)=>{"use strict";var n=r(5685),o=r(9934),i=n.WeakMap;t.exports=o(i)&&/native code/.test(String(i))},2134:(t,e,r)=>{"use strict";var n=r(9068),o=r(9027),i=r(8248),s=r(1890).f;t.exports=function(t){var e=n.Symbol||(n.Symbol={});o(e,t)||s(e,t,{value:i.f(t)})}},8248:(t,e,r)=>{"use strict";var n=r(2442);e.f=n},2442:(t,e,r)=>{"use strict";var n=r(5685),o=r(3921),i=r(9027),s=r(3440),c=r(4086),u=r(16),a=n.Symbol,f=o("wks"),p=u?a.for||a:a&&a.withoutSetter||s;t.exports=function(t){return i(f,t)||(f[t]=c&&i(a,t)?a[t]:p("Symbol."+t)),f[t]}},3533:(t,e,r)=>{"use strict";var n=r(4715),o=r(1727),i=r(3863),s=r(1350),c=r(5895),u=r(3010),a=r(7151),f=r(1567),p=r(2071),l=r(1794),v=r(9614),h=r(81),d=r(2442)("toStringTag"),y=Error,g=[].push,m=function(t,e){var r,n=o(b,this);s?r=s(new y,n?i(this):b):(r=n?this:u(b),a(r,d,"Error")),void 0!==e&&a(r,"message",h(e)),l(r,m,r.stack,1),arguments.length>2&&p(r,arguments[2]);var c=[];return v(t,g,{that:c}),a(r,"errors",c),r};s?s(m,y):c(m,y,{name:!0});var b=m.prototype=u(y.prototype,{constructor:f(1,m),message:f(1,""),name:f(1,"AggregateError")});n({global:!0,constructor:!0,arity:2},{AggregateError:m})},6864:(t,e,r)=>{"use strict";r(3533)},1845:(t,e,r)=>{"use strict";var n=r(4715),o=r(9353),i=r(3527),s=r(9611),c=r(2962),u=r(7165),a=r(9939),f=r(981),p=r(7265),l=r(8388),v=r(2442),h=r(5131),d=v("isConcatSpreadable"),y=h>=51||!o((function(){var t=[];return t[d]=!1,t.concat()[0]!==t})),g=function(t){if(!s(t))return!1;var e=t[d];return void 0!==e?!!e:i(t)};n({target:"Array",proto:!0,arity:1,forced:!y||!l("concat")},{concat:function(t){var e,r,n,o,i,s=c(this),l=p(s,0),v=0;for(e=-1,n=arguments.length;e<n;e++)if(g(i=-1===e?s:arguments[e]))for(o=u(i),a(v+o),r=0;r<o;r++,v++)r in i&&f(l,v,i[r]);else a(v+1),f(l,v++,i);return l.length=v,l}})},1997:(t,e,r)=>{"use strict";var n=r(3747),o=r(6888),i=r(9234),s=r(4084),c=r(1890).f,u=r(6483),a=r(7474),f=r(4081),p=r(3794),l="Array Iterator",v=s.set,h=s.getterFor(l);t.exports=u(Array,"Array",(function(t,e){v(this,{type:l,target:n(t),index:0,kind:e})}),(function(){var t=h(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=void 0,a(void 0,!0);switch(t.kind){case"keys":return a(r,!1);case"values":return a(e[r],!1)}return a([r,e[r]],!1)}),"values");var d=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!f&&p&&"values"!==d.name)try{c(d,"name",{value:"values"})}catch(t){}},8242:()=>{},8791:(t,e,r)=>{"use strict";var n=r(4715),o=r(7192),i=r(145),s=r(3417),c=r(2537),u=r(9353),a=r(9934),f=r(205),p=r(2076),l=r(9647),v=r(4086),h=String,d=o("JSON","stringify"),y=c(/./.exec),g=c("".charAt),m=c("".charCodeAt),b=c("".replace),x=c(1..toString),w=/[\uD800-\uDFFF]/g,S=/^[\uD800-\uDBFF]$/,O=/^[\uDC00-\uDFFF]$/,T=!v||u((function(){var t=o("Symbol")("stringify detection");return"[null]"!==d([t])||"{}"!==d({a:t})||"{}"!==d(Object(t))})),j=u((function(){return'"\\udf06\\ud834"'!==d("\udf06\ud834")||'"\\udead"'!==d("\udead")})),P=function(t,e){var r=p(arguments),n=l(e);if(a(n)||void 0!==t&&!f(t))return r[1]=function(t,e){if(a(n)&&(e=s(n,this,h(t),e)),!f(e))return e},i(d,null,r)},E=function(t,e,r){var n=g(r,e-1),o=g(r,e+1);return y(S,t)&&!y(O,o)||y(O,t)&&!y(S,n)?"\\u"+x(m(t,0),16):t};d&&n({target:"JSON",stat:!0,arity:3,forced:T||j},{stringify:function(t,e,r){var n=p(arguments),o=i(T?P:d,null,n);return j&&"string"==typeof o?b(o,w,E):o}})},8518:(t,e,r)=>{"use strict";var n=r(5685);r(4196)(n.JSON,"JSON",!0)},9786:()=>{},9727:(t,e,r)=>{"use strict";var n=r(4715),o=r(3794),i=r(1890).f;n({target:"Object",stat:!0,forced:Object.defineProperty!==i,sham:!o},{defineProperty:i})},465:(t,e,r)=>{"use strict";var n=r(4715),o=r(4086),i=r(9353),s=r(6953),c=r(2962);n({target:"Object",stat:!0,forced:!o||i((function(){s.f(1)}))},{getOwnPropertySymbols:function(t){var e=s.f;return e?e(c(t)):[]}})},6069:()=>{},8795:(t,e,r)=>{"use strict";var n=r(4715),o=r(3417),i=r(5935),s=r(2157),c=r(3183),u=r(9614);n({target:"Promise",stat:!0,forced:r(7290)},{allSettled:function(t){var e=this,r=s.f(e),n=r.resolve,a=r.reject,f=c((function(){var r=i(e.resolve),s=[],c=0,a=1;u(t,(function(t){var i=c++,u=!1;a++,o(r,e,t).then((function(t){u||(u=!0,s[i]={status:"fulfilled",value:t},--a||n(s))}),(function(t){u||(u=!0,s[i]={status:"rejected",reason:t},--a||n(s))}))})),--a||n(s)}));return f.error&&a(f.value),r.promise}})},5840:(t,e,r)=>{"use strict";var n=r(4715),o=r(3417),i=r(5935),s=r(2157),c=r(3183),u=r(9614);n({target:"Promise",stat:!0,forced:r(7290)},{all:function(t){var e=this,r=s.f(e),n=r.resolve,a=r.reject,f=c((function(){var r=i(e.resolve),s=[],c=0,f=1;u(t,(function(t){var i=c++,u=!1;f++,o(r,e,t).then((function(t){u||(u=!0,s[i]=t,--f||n(s))}),a)})),--f||n(s)}));return f.error&&a(f.value),r.promise}})},3233:(t,e,r)=>{"use strict";var n=r(4715),o=r(3417),i=r(5935),s=r(7192),c=r(2157),u=r(3183),a=r(9614),f=r(7290),p="No one promise resolved";n({target:"Promise",stat:!0,forced:f},{any:function(t){var e=this,r=s("AggregateError"),n=c.f(e),f=n.resolve,l=n.reject,v=u((function(){var n=i(e.resolve),s=[],c=0,u=1,v=!1;a(t,(function(t){var i=c++,a=!1;u++,o(n,e,t).then((function(t){a||v||(v=!0,f(t))}),(function(t){a||v||(a=!0,s[i]=t,--u||l(new r(s,p)))}))})),--u||l(new r(s,p))}));return v.error&&l(v.value),n.promise}})},4168:(t,e,r)=>{"use strict";var n=r(4715),o=r(4081),i=r(4865).CONSTRUCTOR,s=r(3159),c=r(7192),u=r(9934),a=r(1733),f=s&&s.prototype;if(n({target:"Promise",proto:!0,forced:i,real:!0},{catch:function(t){return this.then(void 0,t)}}),!o&&u(s)){var p=c("Promise").prototype.catch;f.catch!==p&&a(f,"catch",p,{unsafe:!0})}},1282:(t,e,r)=>{"use strict";var n,o,i,s=r(4715),c=r(4081),u=r(4408),a=r(5685),f=r(3417),p=r(1733),l=r(1350),v=r(4196),h=r(4799),d=r(5935),y=r(9934),g=r(9611),m=r(927),b=r(9022),x=r(6727).set,w=r(5996),S=r(2210),O=r(3183),T=r(5721),j=r(4084),P=r(3159),E=r(4865),k=r(2157),_="Promise",A=E.CONSTRUCTOR,I=E.REJECTION_EVENT,R=E.SUBCLASSING,C=j.getterFor(_),L=j.set,D=P&&P.prototype,M=P,N=D,F=a.TypeError,G=a.document,U=a.process,B=k.f,V=B,W=!!(G&&G.createEvent&&a.dispatchEvent),z="unhandledrejection",H=function(t){var e;return!(!g(t)||!y(e=t.then))&&e},K=function(t,e){var r,n,o,i=e.value,s=1===e.state,c=s?t.ok:t.fail,u=t.resolve,a=t.reject,p=t.domain;try{c?(s||(2===e.rejection&&X(e),e.rejection=1),!0===c?r=i:(p&&p.enter(),r=c(i),p&&(p.exit(),o=!0)),r===t.promise?a(new F("Promise-chain cycle")):(n=H(r))?f(n,r,u,a):u(r)):a(i)}catch(t){p&&!o&&p.exit(),a(t)}},q=function(t,e){t.notified||(t.notified=!0,w((function(){for(var r,n=t.reactions;r=n.get();)K(r,t);t.notified=!1,e&&!t.rejection&&Y(t)})))},J=function(t,e,r){var n,o;W?((n=G.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),a.dispatchEvent(n)):n={promise:e,reason:r},!I&&(o=a["on"+t])?o(n):t===z&&S("Unhandled promise rejection",r)},Y=function(t){f(x,a,(function(){var e,r=t.facade,n=t.value;if(Q(t)&&(e=O((function(){u?U.emit("unhandledRejection",n,r):J(z,r,n)})),t.rejection=u||Q(t)?2:1,e.error))throw e.value}))},Q=function(t){return 1!==t.rejection&&!t.parent},X=function(t){f(x,a,(function(){var e=t.facade;u?U.emit("rejectionHandled",e):J("rejectionhandled",e,t.value)}))},$=function(t,e,r){return function(n){t(e,n,r)}},Z=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,q(t,!0))},tt=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new F("Promise can't be resolved itself");var n=H(e);n?w((function(){var r={done:!1};try{f(n,e,$(tt,r,t),$(Z,r,t))}catch(e){Z(r,e,t)}})):(t.value=e,t.state=1,q(t,!1))}catch(e){Z({done:!1},e,t)}}};if(A&&(N=(M=function(t){m(this,N),d(t),f(n,this);var e=C(this);try{t($(tt,e),$(Z,e))}catch(t){Z(e,t)}}).prototype,(n=function(t){L(this,{type:_,done:!1,notified:!1,parent:!1,reactions:new T,rejection:!1,state:0,value:void 0})}).prototype=p(N,"then",(function(t,e){var r=C(this),n=B(b(this,M));return r.parent=!0,n.ok=!y(t)||t,n.fail=y(e)&&e,n.domain=u?U.domain:void 0,0===r.state?r.reactions.add(n):w((function(){K(n,r)})),n.promise})),o=function(){var t=new n,e=C(t);this.promise=t,this.resolve=$(tt,e),this.reject=$(Z,e)},k.f=B=function(t){return t===M||void 0===t?new o(t):V(t)},!c&&y(P)&&D!==Object.prototype)){i=D.then,R||p(D,"then",(function(t,e){var r=this;return new M((function(t,e){f(i,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete D.constructor}catch(t){}l&&l(D,N)}s({global:!0,constructor:!0,wrap:!0,forced:A},{Promise:M}),v(M,_,!1,!0),h(_)},6028:(t,e,r)=>{"use strict";var n=r(4715),o=r(4081),i=r(3159),s=r(9353),c=r(7192),u=r(9934),a=r(9022),f=r(5712),p=r(1733),l=i&&i.prototype;if(n({target:"Promise",proto:!0,real:!0,forced:!!i&&s((function(){l.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var e=a(this,c("Promise")),r=u(t);return this.then(r?function(r){return f(e,t()).then((function(){return r}))}:t,r?function(r){return f(e,t()).then((function(){throw r}))}:t)}}),!o&&u(i)){var v=c("Promise").prototype.finally;l.finally!==v&&p(l,"finally",v,{unsafe:!0})}},9927:(t,e,r)=>{"use strict";r(1282),r(5840),r(4168),r(1228),r(1739),r(1099)},1228:(t,e,r)=>{"use strict";var n=r(4715),o=r(3417),i=r(5935),s=r(2157),c=r(3183),u=r(9614);n({target:"Promise",stat:!0,forced:r(7290)},{race:function(t){var e=this,r=s.f(e),n=r.reject,a=c((function(){var s=i(e.resolve);u(t,(function(t){o(s,e,t).then(r.resolve,n)}))}));return a.error&&n(a.value),r.promise}})},1739:(t,e,r)=>{"use strict";var n=r(4715),o=r(3417),i=r(2157);n({target:"Promise",stat:!0,forced:r(4865).CONSTRUCTOR},{reject:function(t){var e=i.f(this);return o(e.reject,void 0,t),e.promise}})},1099:(t,e,r)=>{"use strict";var n=r(4715),o=r(7192),i=r(4081),s=r(3159),c=r(4865).CONSTRUCTOR,u=r(5712),a=o("Promise"),f=i&&!c;n({target:"Promise",stat:!0,forced:i||c},{resolve:function(t){return u(f&&this===a?s:this,t)}})},8840:(t,e,r)=>{"use strict";var n=r(4715),o=r(2157);n({target:"Promise",stat:!0},{withResolvers:function(){var t=o.f(this);return{promise:t.promise,resolve:t.resolve,reject:t.reject}}})},6716:()=>{},1345:(t,e,r)=>{"use strict";var n=r(5202).charAt,o=r(1182),i=r(4084),s=r(6483),c=r(7474),u="String Iterator",a=i.set,f=i.getterFor(u);s(String,"String",(function(t){a(this,{type:u,string:o(t),index:0})}),(function(){var t,e=f(this),r=e.string,o=e.index;return o>=r.length?c(void 0,!0):(t=n(r,o),e.index+=t.length,c(t,!1))}))},342:(t,e,r)=>{"use strict";r(2134)("asyncIterator")},3971:(t,e,r)=>{"use strict";var n=r(4715),o=r(5685),i=r(3417),s=r(2537),c=r(4081),u=r(3794),a=r(4086),f=r(9353),p=r(9027),l=r(1727),v=r(8879),h=r(3747),d=r(1525),y=r(1182),g=r(1567),m=r(3010),b=r(7508),x=r(4582),w=r(7195),S=r(6953),O=r(5396),T=r(1890),j=r(7832),P=r(9106),E=r(1733),k=r(3089),_=r(3921),A=r(3287),I=r(9775),R=r(3440),C=r(2442),L=r(8248),D=r(2134),M=r(9681),N=r(4196),F=r(4084),G=r(2503).forEach,U=A("hidden"),B="Symbol",V="prototype",W=F.set,z=F.getterFor(B),H=Object[V],K=o.Symbol,q=K&&K[V],J=o.RangeError,Y=o.TypeError,Q=o.QObject,X=O.f,$=T.f,Z=w.f,tt=P.f,et=s([].push),rt=_("symbols"),nt=_("op-symbols"),ot=_("wks"),it=!Q||!Q[V]||!Q[V].findChild,st=function(t,e,r){var n=X(H,e);n&&delete H[e],$(t,e,r),n&&t!==H&&$(H,e,n)},ct=u&&f((function(){return 7!==m($({},"a",{get:function(){return $(this,"a",{value:7}).a}})).a}))?st:$,ut=function(t,e){var r=rt[t]=m(q);return W(r,{type:B,tag:t,description:e}),u||(r.description=e),r},at=function(t,e,r){t===H&&at(nt,e,r),v(t);var n=d(e);return v(r),p(rt,n)?(r.enumerable?(p(t,U)&&t[U][n]&&(t[U][n]=!1),r=m(r,{enumerable:g(0,!1)})):(p(t,U)||$(t,U,g(1,{})),t[U][n]=!0),ct(t,n,r)):$(t,n,r)},ft=function(t,e){v(t);var r=h(e),n=b(r).concat(ht(r));return G(n,(function(e){u&&!i(pt,r,e)||at(t,e,r[e])})),t},pt=function(t){var e=d(t),r=i(tt,this,e);return!(this===H&&p(rt,e)&&!p(nt,e))&&(!(r||!p(this,e)||!p(rt,e)||p(this,U)&&this[U][e])||r)},lt=function(t,e){var r=h(t),n=d(e);if(r!==H||!p(rt,n)||p(nt,n)){var o=X(r,n);return!o||!p(rt,n)||p(r,U)&&r[U][n]||(o.enumerable=!0),o}},vt=function(t){var e=Z(h(t)),r=[];return G(e,(function(t){p(rt,t)||p(I,t)||et(r,t)})),r},ht=function(t){var e=t===H,r=Z(e?nt:h(t)),n=[];return G(r,(function(t){!p(rt,t)||e&&!p(H,t)||et(n,rt[t])})),n};a||(E(q=(K=function(){if(l(q,this))throw new Y("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?y(arguments[0]):void 0,e=R(t),r=function(t){var n=void 0===this?o:this;n===H&&i(r,nt,t),p(n,U)&&p(n[U],e)&&(n[U][e]=!1);var s=g(1,t);try{ct(n,e,s)}catch(t){if(!(t instanceof J))throw t;st(n,e,s)}};return u&&it&&ct(H,e,{configurable:!0,set:r}),ut(e,t)})[V],"toString",(function(){return z(this).tag})),E(K,"withoutSetter",(function(t){return ut(R(t),t)})),P.f=pt,T.f=at,j.f=ft,O.f=lt,x.f=w.f=vt,S.f=ht,L.f=function(t){return ut(C(t),t)},u&&(k(q,"description",{configurable:!0,get:function(){return z(this).description}}),c||E(H,"propertyIsEnumerable",pt,{unsafe:!0}))),n({global:!0,constructor:!0,wrap:!0,forced:!a,sham:!a},{Symbol:K}),G(b(ot),(function(t){D(t)})),n({target:B,stat:!0,forced:!a},{useSetter:function(){it=!0},useSimple:function(){it=!1}}),n({target:"Object",stat:!0,forced:!a,sham:!u},{create:function(t,e){return void 0===e?m(t):ft(m(t),e)},defineProperty:at,defineProperties:ft,getOwnPropertyDescriptor:lt}),n({target:"Object",stat:!0,forced:!a},{getOwnPropertyNames:vt}),M(),N(K,B),I[U]=!0},8861:()=>{},5201:(t,e,r)=>{"use strict";var n=r(4715),o=r(7192),i=r(9027),s=r(1182),c=r(3921),u=r(5731),a=c("string-to-symbol-registry"),f=c("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!u},{for:function(t){var e=s(t);if(i(a,e))return a[e];var r=o("Symbol")(e);return a[e]=r,f[r]=e,r}})},3092:(t,e,r)=>{"use strict";r(2134)("hasInstance")},6538:(t,e,r)=>{"use strict";r(2134)("isConcatSpreadable")},459:(t,e,r)=>{"use strict";r(2134)("iterator")},1967:(t,e,r)=>{"use strict";r(3971),r(5201),r(3274),r(8791),r(465)},3274:(t,e,r)=>{"use strict";var n=r(4715),o=r(9027),i=r(205),s=r(1028),c=r(3921),u=r(5731),a=c("symbol-to-string-registry");n({target:"Symbol",stat:!0,forced:!u},{keyFor:function(t){if(!i(t))throw new TypeError(s(t)+" is not a symbol");if(o(a,t))return a[t]}})},3236:(t,e,r)=>{"use strict";r(2134)("matchAll")},2303:(t,e,r)=>{"use strict";r(2134)("match")},1654:(t,e,r)=>{"use strict";r(2134)("replace")},4833:(t,e,r)=>{"use strict";r(2134)("search")},316:(t,e,r)=>{"use strict";r(2134)("species")},6925:(t,e,r)=>{"use strict";r(2134)("split")},3135:(t,e,r)=>{"use strict";var n=r(2134),o=r(9681);n("toPrimitive"),o()},9390:(t,e,r)=>{"use strict";var n=r(7192),o=r(2134),i=r(4196);o("toStringTag"),i(n("Symbol"),"Symbol")},5938:(t,e,r)=>{"use strict";r(2134)("unscopables")},3705:(t,e,r)=>{"use strict";var n=r(2442),o=r(1890).f,i=n("metadata"),s=Function.prototype;void 0===s[i]&&o(s,i,{value:null})},1935:(t,e,r)=>{"use strict";r(2134)("asyncDispose")},1944:(t,e,r)=>{"use strict";r(2134)("dispose")},3361:(t,e,r)=>{"use strict";r(4715)({target:"Symbol",stat:!0},{isRegisteredSymbol:r(3203)})},6714:(t,e,r)=>{"use strict";r(4715)({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:r(3203)})},1823:(t,e,r)=>{"use strict";r(4715)({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:r(9003)})},5704:(t,e,r)=>{"use strict";r(4715)({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:r(9003)})},4163:(t,e,r)=>{"use strict";r(2134)("matcher")},6206:(t,e,r)=>{"use strict";r(2134)("metadataKey")},5539:(t,e,r)=>{"use strict";r(2134)("metadata")},6499:(t,e,r)=>{"use strict";r(2134)("observable")},1548:(t,e,r)=>{"use strict";r(2134)("patternMatch")},1666:(t,e,r)=>{"use strict";r(2134)("replaceAll")},7483:(t,e,r)=>{"use strict";r(1997);var n=r(8920),o=r(5685),i=r(4196),s=r(9234);for(var c in n)i(o[c],c),s[c]=s.Array},5602:(t,e,r)=>{"use strict";var n=r(8641);t.exports=n},2948:(t,e,r)=>{"use strict";var n=r(8462);r(7483),t.exports=n},4454:(t,e,r)=>{"use strict";var n=r(3941);r(7483),t.exports=n},4690:(t,e,r)=>{"use strict";var n=r(4101);r(7483),t.exports=n},7263:(t,e,r)=>{"use strict";var n=r(7548);t.exports=n},509:(t,e,r)=>{"use strict";var n=r(9985),o=r(3691),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a function")}},2655:(t,e,r)=>{"use strict";var n=r(9429),o=r(3691),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a constructor")}},3550:(t,e,r)=>{"use strict";var n=r(9985),o=String,i=TypeError;t.exports=function(t){if("object"==typeof t||n(t))return t;throw new i("Can't set "+o(t)+" as a prototype")}},7370:(t,e,r)=>{"use strict";var n=r(4201),o=r(5391),i=r(2560).f,s=n("unscopables"),c=Array.prototype;void 0===c[s]&&i(c,s,{configurable:!0,value:o(null)}),t.exports=function(t){c[s][t]=!0}},767:(t,e,r)=>{"use strict";var n=r(3622),o=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw new o("Incorrect invocation")}},5027:(t,e,r)=>{"use strict";var n=r(8999),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not an object")}},4328:(t,e,r)=>{"use strict";var n=r(5290),o=r(7578),i=r(6310),s=function(t){return function(e,r,s){var c,u=n(e),a=i(u),f=o(s,a);if(t&&r!=r){for(;a>f;)if((c=u[f++])!=c)return!0}else for(;a>f;f++)if((t||f in u)&&u[f]===r)return t||f||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},6004:(t,e,r)=>{"use strict";var n=r(8844);t.exports=n([].slice)},6431:(t,e,r)=>{"use strict";var n=r(4201)("iterator"),o=!1;try{var i=0,s={next:function(){return{done:!!i++}},return:function(){o=!0}};s[n]=function(){return this},Array.from(s,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!o)return!1}catch(t){return!1}var r=!1;try{var i={};i[n]=function(){return{next:function(){return{done:r=!0}}}},t(i)}catch(t){}return r}},6648:(t,e,r)=>{"use strict";var n=r(8844),o=n({}.toString),i=n("".slice);t.exports=function(t){return i(o(t),8,-1)}},926:(t,e,r)=>{"use strict";var n=r(3043),o=r(9985),i=r(6648),s=r(4201)("toStringTag"),c=Object,u="Arguments"===i(function(){return arguments}());t.exports=n?i:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=c(t),s))?r:u?i(e):"Object"===(n=i(e))&&o(e.callee)?"Arguments":n}},8758:(t,e,r)=>{"use strict";var n=r(6812),o=r(9152),i=r(2474),s=r(2560);t.exports=function(t,e,r){for(var c=o(e),u=s.f,a=i.f,f=0;f<c.length;f++){var p=c[f];n(t,p)||r&&n(r,p)||u(t,p,a(e,p))}}},1748:(t,e,r)=>{"use strict";var n=r(3689);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},7807:t=>{"use strict";t.exports=function(t,e){return{value:t,done:e}}},5773:(t,e,r)=>{"use strict";var n=r(7697),o=r(2560),i=r(5684);t.exports=n?function(t,e,r){return o.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},5684:t=>{"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},2148:(t,e,r)=>{"use strict";var n=r(8702),o=r(2560);t.exports=function(t,e,r){return r.get&&n(r.get,e,{getter:!0}),r.set&&n(r.set,e,{setter:!0}),o.f(t,e,r)}},1880:(t,e,r)=>{"use strict";var n=r(9985),o=r(2560),i=r(8702),s=r(5014);t.exports=function(t,e,r,c){c||(c={});var u=c.enumerable,a=void 0!==c.name?c.name:e;if(n(r)&&i(r,a,c),c.global)u?t[e]=r:s(e,r);else{try{c.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:o.f(t,e,{value:r,enumerable:!1,configurable:!c.nonConfigurable,writable:!c.nonWritable})}return t}},5014:(t,e,r)=>{"use strict";var n=r(9037),o=Object.defineProperty;t.exports=function(t,e){try{o(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},7697:(t,e,r)=>{"use strict";var n=r(3689);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},2659:t=>{"use strict";var e="object"==typeof document&&document.all,r=void 0===e&&void 0!==e;t.exports={all:e,IS_HTMLDDA:r}},6420:(t,e,r)=>{"use strict";var n=r(9037),o=r(8999),i=n.document,s=o(i)&&o(i.createElement);t.exports=function(t){return s?i.createElement(t):{}}},6338:t=>{"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},3265:(t,e,r)=>{"use strict";var n=r(6420)("span").classList,o=n&&n.constructor&&n.constructor.prototype;t.exports=o===Object.prototype?void 0:o},2532:(t,e,r)=>{"use strict";var n=r(8563),o=r(806);t.exports=!n&&!o&&"object"==typeof window&&"object"==typeof document},8563:t=>{"use strict";t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},3221:(t,e,r)=>{"use strict";var n=r(71);t.exports=/ipad|iphone|ipod/i.test(n)&&"undefined"!=typeof Pebble},4764:(t,e,r)=>{"use strict";var n=r(71);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(n)},806:(t,e,r)=>{"use strict";var n=r(9037),o=r(6648);t.exports="process"===o(n.process)},7486:(t,e,r)=>{"use strict";var n=r(71);t.exports=/web0s(?!.*chrome)/i.test(n)},71:t=>{"use strict";t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},3615:(t,e,r)=>{"use strict";var n,o,i=r(9037),s=r(71),c=i.process,u=i.Deno,a=c&&c.versions||u&&u.version,f=a&&a.v8;f&&(o=(n=f.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&s&&(!(n=s.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=s.match(/Chrome\/(\d+)/))&&(o=+n[1]),t.exports=o},2739:t=>{"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},6610:(t,e,r)=>{"use strict";var n=r(8844),o=Error,i=n("".replace),s=String(new o("zxcasd").stack),c=/\n\s*at [^:]*:[^\n]*/,u=c.test(s);t.exports=function(t,e){if(u&&"string"==typeof t&&!o.prepareStackTrace)for(;e--;)t=i(t,c,"");return t}},5411:(t,e,r)=>{"use strict";var n=r(5773),o=r(6610),i=r(9599),s=Error.captureStackTrace;t.exports=function(t,e,r,c){i&&(s?s(t,e):n(t,"stack",o(r,c)))}},9599:(t,e,r)=>{"use strict";var n=r(3689),o=r(5684);t.exports=!n((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",o(1,7)),7!==t.stack)}))},9989:(t,e,r)=>{"use strict";var n=r(9037),o=r(2474).f,i=r(5773),s=r(1880),c=r(5014),u=r(8758),a=r(5266);t.exports=function(t,e){var r,f,p,l,v,h=t.target,d=t.global,y=t.stat;if(r=d?n:y?n[h]||c(h,{}):(n[h]||{}).prototype)for(f in e){if(l=e[f],p=t.dontCallGetSet?(v=o(r,f))&&v.value:r[f],!a(d?f:h+(y?".":"#")+f,t.forced)&&void 0!==p){if(typeof l==typeof p)continue;u(l,p)}(t.sham||p&&p.sham)&&i(l,"sham",!0),s(r,f,l,t)}}},3689:t=>{"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},1735:(t,e,r)=>{"use strict";var n=r(7215),o=Function.prototype,i=o.apply,s=o.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?s.bind(i):function(){return s.apply(i,arguments)})},4071:(t,e,r)=>{"use strict";var n=r(6576),o=r(509),i=r(7215),s=n(n.bind);t.exports=function(t,e){return o(t),void 0===e?t:i?s(t,e):function(){return t.apply(e,arguments)}}},7215:(t,e,r)=>{"use strict";var n=r(3689);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},2615:(t,e,r)=>{"use strict";var n=r(7215),o=Function.prototype.call;t.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},1236:(t,e,r)=>{"use strict";var n=r(7697),o=r(6812),i=Function.prototype,s=n&&Object.getOwnPropertyDescriptor,c=o(i,"name"),u=c&&"something"===function(){}.name,a=c&&(!n||n&&s(i,"name").configurable);t.exports={EXISTS:c,PROPER:u,CONFIGURABLE:a}},2743:(t,e,r)=>{"use strict";var n=r(8844),o=r(509);t.exports=function(t,e,r){try{return n(o(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},6576:(t,e,r)=>{"use strict";var n=r(6648),o=r(8844);t.exports=function(t){if("Function"===n(t))return o(t)}},8844:(t,e,r)=>{"use strict";var n=r(7215),o=Function.prototype,i=o.call,s=n&&o.bind.bind(i,i);t.exports=n?s:function(t){return function(){return i.apply(t,arguments)}}},6058:(t,e,r)=>{"use strict";var n=r(9037),o=r(9985);t.exports=function(t,e){return arguments.length<2?(r=n[t],o(r)?r:void 0):n[t]&&n[t][e];var r}},1664:(t,e,r)=>{"use strict";var n=r(926),o=r(4849),i=r(5726),s=r(9478),c=r(4201)("iterator");t.exports=function(t){if(!i(t))return o(t,c)||o(t,"@@iterator")||s[n(t)]}},5185:(t,e,r)=>{"use strict";var n=r(2615),o=r(509),i=r(5027),s=r(3691),c=r(1664),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?c(t):e;if(o(r))return i(n(r,t));throw new u(s(t)+" is not iterable")}},4849:(t,e,r)=>{"use strict";var n=r(509),o=r(5726);t.exports=function(t,e){var r=t[e];return o(r)?void 0:n(r)}},9037:function(t,e,r){"use strict";var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},6812:(t,e,r)=>{"use strict";var n=r(8844),o=r(690),i=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return i(o(t),e)}},7248:t=>{"use strict";t.exports={}},920:t=>{"use strict";t.exports=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}}},2688:(t,e,r)=>{"use strict";var n=r(6058);t.exports=n("document","documentElement")},8506:(t,e,r)=>{"use strict";var n=r(7697),o=r(3689),i=r(6420);t.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},4413:(t,e,r)=>{"use strict";var n=r(8844),o=r(3689),i=r(6648),s=Object,c=n("".split);t.exports=o((function(){return!s("z").propertyIsEnumerable(0)}))?function(t){return"String"===i(t)?c(t,""):s(t)}:s},3457:(t,e,r)=>{"use strict";var n=r(9985),o=r(8999),i=r(9385);t.exports=function(t,e,r){var s,c;return i&&n(s=e.constructor)&&s!==r&&o(c=s.prototype)&&c!==r.prototype&&i(t,c),t}},6738:(t,e,r)=>{"use strict";var n=r(8844),o=r(9985),i=r(4091),s=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(t){return s(t)}),t.exports=i.inspectSource},2570:(t,e,r)=>{"use strict";var n=r(8999),o=r(5773);t.exports=function(t,e){n(e)&&"cause"in e&&o(t,"cause",e.cause)}},618:(t,e,r)=>{"use strict";var n,o,i,s=r(9834),c=r(9037),u=r(8999),a=r(5773),f=r(6812),p=r(4091),l=r(2713),v=r(7248),h="Object already initialized",d=c.TypeError,y=c.WeakMap;if(s||p.state){var g=p.state||(p.state=new y);g.get=g.get,g.has=g.has,g.set=g.set,n=function(t,e){if(g.has(t))throw new d(h);return e.facade=t,g.set(t,e),e},o=function(t){return g.get(t)||{}},i=function(t){return g.has(t)}}else{var m=l("state");v[m]=!0,n=function(t,e){if(f(t,m))throw new d(h);return e.facade=t,a(t,m,e),e},o=function(t){return f(t,m)?t[m]:{}},i=function(t){return f(t,m)}}t.exports={set:n,get:o,has:i,enforce:function(t){return i(t)?o(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=o(e)).type!==t)throw new d("Incompatible receiver, "+t+" required");return r}}}},3292:(t,e,r)=>{"use strict";var n=r(4201),o=r(9478),i=n("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||s[i]===t)}},9985:(t,e,r)=>{"use strict";var n=r(2659),o=n.all;t.exports=n.IS_HTMLDDA?function(t){return"function"==typeof t||t===o}:function(t){return"function"==typeof t}},9429:(t,e,r)=>{"use strict";var n=r(8844),o=r(3689),i=r(9985),s=r(926),c=r(6058),u=r(6738),a=function(){},f=[],p=c("Reflect","construct"),l=/^\s*(?:class|function)\b/,v=n(l.exec),h=!l.test(a),d=function(t){if(!i(t))return!1;try{return p(a,f,t),!0}catch(t){return!1}},y=function(t){if(!i(t))return!1;switch(s(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return h||!!v(l,u(t))}catch(t){return!0}};y.sham=!0,t.exports=!p||o((function(){var t;return d(d.call)||!d(Object)||!d((function(){t=!0}))||t}))?y:d},5266:(t,e,r)=>{"use strict";var n=r(3689),o=r(9985),i=/#|\.prototype\./,s=function(t,e){var r=u[c(t)];return r===f||r!==a&&(o(e)?n(e):!!e)},c=s.normalize=function(t){return String(t).replace(i,".").toLowerCase()},u=s.data={},a=s.NATIVE="N",f=s.POLYFILL="P";t.exports=s},5726:t=>{"use strict";t.exports=function(t){return null==t}},8999:(t,e,r)=>{"use strict";var n=r(9985),o=r(2659),i=o.all;t.exports=o.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:n(t)||t===i}:function(t){return"object"==typeof t?null!==t:n(t)}},3931:t=>{"use strict";t.exports=!1},734:(t,e,r)=>{"use strict";var n=r(6058),o=r(9985),i=r(3622),s=r(9525),c=Object;t.exports=s?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return o(e)&&i(e.prototype,c(t))}},8734:(t,e,r)=>{"use strict";var n=r(4071),o=r(2615),i=r(5027),s=r(3691),c=r(3292),u=r(6310),a=r(3622),f=r(5185),p=r(1664),l=r(2125),v=TypeError,h=function(t,e){this.stopped=t,this.result=e},d=h.prototype;t.exports=function(t,e,r){var y,g,m,b,x,w,S,O=r&&r.that,T=!(!r||!r.AS_ENTRIES),j=!(!r||!r.IS_RECORD),P=!(!r||!r.IS_ITERATOR),E=!(!r||!r.INTERRUPTED),k=n(e,O),_=function(t){return y&&l(y,"normal",t),new h(!0,t)},A=function(t){return T?(i(t),E?k(t[0],t[1],_):k(t[0],t[1])):E?k(t,_):k(t)};if(j)y=t.iterator;else if(P)y=t;else{if(!(g=p(t)))throw new v(s(t)+" is not iterable");if(c(g)){for(m=0,b=u(t);b>m;m++)if((x=A(t[m]))&&a(d,x))return x;return new h(!1)}y=f(t,g)}for(w=j?t.next:y.next;!(S=o(w,y)).done;){try{x=A(S.value)}catch(t){l(y,"throw",t)}if("object"==typeof x&&x&&a(d,x))return x}return new h(!1)}},2125:(t,e,r)=>{"use strict";var n=r(2615),o=r(5027),i=r(4849);t.exports=function(t,e,r){var s,c;o(t);try{if(!(s=i(t,"return"))){if("throw"===e)throw r;return r}s=n(s,t)}catch(t){c=!0,s=t}if("throw"===e)throw r;if(c)throw s;return o(s),r}},974:(t,e,r)=>{"use strict";var n=r(2013).IteratorPrototype,o=r(5391),i=r(5684),s=r(5997),c=r(9478),u=function(){return this};t.exports=function(t,e,r,a){var f=e+" Iterator";return t.prototype=o(n,{next:i(+!a,r)}),s(t,f,!1,!0),c[f]=u,t}},1934:(t,e,r)=>{"use strict";var n=r(9989),o=r(2615),i=r(3931),s=r(1236),c=r(9985),u=r(974),a=r(1868),f=r(9385),p=r(5997),l=r(5773),v=r(1880),h=r(4201),d=r(9478),y=r(2013),g=s.PROPER,m=s.CONFIGURABLE,b=y.IteratorPrototype,x=y.BUGGY_SAFARI_ITERATORS,w=h("iterator"),S="keys",O="values",T="entries",j=function(){return this};t.exports=function(t,e,r,s,h,y,P){u(r,e,s);var E,k,_,A=function(t){if(t===h&&D)return D;if(!x&&t&&t in C)return C[t];switch(t){case S:case O:case T:return function(){return new r(this,t)}}return function(){return new r(this)}},I=e+" Iterator",R=!1,C=t.prototype,L=C[w]||C["@@iterator"]||h&&C[h],D=!x&&L||A(h),M="Array"===e&&C.entries||L;if(M&&(E=a(M.call(new t)))!==Object.prototype&&E.next&&(i||a(E)===b||(f?f(E,b):c(E[w])||v(E,w,j)),p(E,I,!0,!0),i&&(d[I]=j)),g&&h===O&&L&&L.name!==O&&(!i&&m?l(C,"name",O):(R=!0,D=function(){return o(L,this)})),h)if(k={values:A(O),keys:y?D:A(S),entries:A(T)},P)for(_ in k)(x||R||!(_ in C))&&v(C,_,k[_]);else n({target:e,proto:!0,forced:x||R},k);return i&&!P||C[w]===D||v(C,w,D,{name:h}),d[e]=D,k}},2013:(t,e,r)=>{"use strict";var n,o,i,s=r(3689),c=r(9985),u=r(8999),a=r(5391),f=r(1868),p=r(1880),l=r(4201),v=r(3931),h=l("iterator"),d=!1;[].keys&&("next"in(i=[].keys())?(o=f(f(i)))!==Object.prototype&&(n=o):d=!0),!u(n)||s((function(){var t={};return n[h].call(t)!==t}))?n={}:v&&(n=a(n)),c(n[h])||p(n,h,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:d}},9478:t=>{"use strict";t.exports={}},6310:(t,e,r)=>{"use strict";var n=r(3126);t.exports=function(t){return n(t.length)}},8702:(t,e,r)=>{"use strict";var n=r(8844),o=r(3689),i=r(9985),s=r(6812),c=r(7697),u=r(1236).CONFIGURABLE,a=r(6738),f=r(618),p=f.enforce,l=f.get,v=String,h=Object.defineProperty,d=n("".slice),y=n("".replace),g=n([].join),m=c&&!o((function(){return 8!==h((function(){}),"length",{value:8}).length})),b=String(String).split("String"),x=t.exports=function(t,e,r){"Symbol("===d(v(e),0,7)&&(e="["+y(v(e),/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!s(t,"name")||u&&t.name!==e)&&(c?h(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&s(r,"arity")&&t.length!==r.arity&&h(t,"length",{value:r.arity});try{r&&s(r,"constructor")&&r.constructor?c&&h(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=p(t);return s(n,"source")||(n.source=g(b,"string"==typeof e?e:"")),t};Function.prototype.toString=x((function(){return i(this)&&l(this).source||a(this)}),"toString")},8828:t=>{"use strict";var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)}},231:(t,e,r)=>{"use strict";var n,o,i,s,c,u=r(9037),a=r(4071),f=r(2474).f,p=r(9886).set,l=r(4410),v=r(4764),h=r(3221),d=r(7486),y=r(806),g=u.MutationObserver||u.WebKitMutationObserver,m=u.document,b=u.process,x=u.Promise,w=f(u,"queueMicrotask"),S=w&&w.value;if(!S){var O=new l,T=function(){var t,e;for(y&&(t=b.domain)&&t.exit();e=O.get();)try{e()}catch(t){throw O.head&&n(),t}t&&t.enter()};v||y||d||!g||!m?!h&&x&&x.resolve?((s=x.resolve(void 0)).constructor=x,c=a(s.then,s),n=function(){c(T)}):y?n=function(){b.nextTick(T)}:(p=a(p,u),n=function(){p(T)}):(o=!0,i=m.createTextNode(""),new g(T).observe(i,{characterData:!0}),n=function(){i.data=o=!o}),S=function(t){O.head||n(),O.add(t)}}t.exports=S},8742:(t,e,r)=>{"use strict";var n=r(509),o=TypeError,i=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw new o("Bad Promise constructor");e=t,r=n})),this.resolve=n(e),this.reject=n(r)};t.exports.f=function(t){return new i(t)}},3841:(t,e,r)=>{"use strict";var n=r(4327);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},5391:(t,e,r)=>{"use strict";var n,o=r(5027),i=r(5255),s=r(2739),c=r(7248),u=r(2688),a=r(6420),f=r(2713),p="prototype",l="script",v=f("IE_PROTO"),h=function(){},d=function(t){return"<"+l+">"+t+"</"+l+">"},y=function(t){t.write(d("")),t.close();var e=t.parentWindow.Object;return t=null,e},g=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;g="undefined"!=typeof document?document.domain&&n?y(n):(e=a("iframe"),r="java"+l+":",e.style.display="none",u.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(d("document.F=Object")),t.close(),t.F):y(n);for(var o=s.length;o--;)delete g[p][s[o]];return g()};c[v]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(h[p]=o(t),r=new h,h[p]=null,r[v]=t):r=g(),void 0===e?r:i.f(r,e)}},5255:(t,e,r)=>{"use strict";var n=r(7697),o=r(5648),i=r(2560),s=r(5027),c=r(5290),u=r(300);e.f=n&&!o?Object.defineProperties:function(t,e){s(t);for(var r,n=c(e),o=u(e),a=o.length,f=0;a>f;)i.f(t,r=o[f++],n[r]);return t}},2560:(t,e,r)=>{"use strict";var n=r(7697),o=r(8506),i=r(5648),s=r(5027),c=r(8360),u=TypeError,a=Object.defineProperty,f=Object.getOwnPropertyDescriptor,p="enumerable",l="configurable",v="writable";e.f=n?i?function(t,e,r){if(s(t),e=c(e),s(r),"function"==typeof t&&"prototype"===e&&"value"in r&&v in r&&!r[v]){var n=f(t,e);n&&n[v]&&(t[e]=r.value,r={configurable:l in r?r[l]:n[l],enumerable:p in r?r[p]:n[p],writable:!1})}return a(t,e,r)}:a:function(t,e,r){if(s(t),e=c(e),s(r),o)try{return a(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},2474:(t,e,r)=>{"use strict";var n=r(7697),o=r(2615),i=r(9556),s=r(5684),c=r(5290),u=r(8360),a=r(6812),f=r(8506),p=Object.getOwnPropertyDescriptor;e.f=n?p:function(t,e){if(t=c(t),e=u(e),f)try{return p(t,e)}catch(t){}if(a(t,e))return s(!o(i.f,t,e),t[e])}},2741:(t,e,r)=>{"use strict";var n=r(4948),o=r(2739).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,o)}},7518:(t,e)=>{"use strict";e.f=Object.getOwnPropertySymbols},1868:(t,e,r)=>{"use strict";var n=r(6812),o=r(9985),i=r(690),s=r(2713),c=r(1748),u=s("IE_PROTO"),a=Object,f=a.prototype;t.exports=c?a.getPrototypeOf:function(t){var e=i(t);if(n(e,u))return e[u];var r=e.constructor;return o(r)&&e instanceof r?r.prototype:e instanceof a?f:null}},3622:(t,e,r)=>{"use strict";var n=r(8844);t.exports=n({}.isPrototypeOf)},4948:(t,e,r)=>{"use strict";var n=r(8844),o=r(6812),i=r(5290),s=r(4328).indexOf,c=r(7248),u=n([].push);t.exports=function(t,e){var r,n=i(t),a=0,f=[];for(r in n)!o(c,r)&&o(n,r)&&u(f,r);for(;e.length>a;)o(n,r=e[a++])&&(~s(f,r)||u(f,r));return f}},300:(t,e,r)=>{"use strict";var n=r(4948),o=r(2739);t.exports=Object.keys||function(t){return n(t,o)}},9556:(t,e)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);e.f=o?function(t){var e=n(this,t);return!!e&&e.enumerable}:r},9385:(t,e,r)=>{"use strict";var n=r(2743),o=r(5027),i=r(3550);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=n(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return o(r),i(n),e?t(r,n):r.__proto__=n,r}}():void 0)},5899:(t,e,r)=>{"use strict";var n=r(2615),o=r(9985),i=r(8999),s=TypeError;t.exports=function(t,e){var r,c;if("string"===e&&o(r=t.toString)&&!i(c=n(r,t)))return c;if(o(r=t.valueOf)&&!i(c=n(r,t)))return c;if("string"!==e&&o(r=t.toString)&&!i(c=n(r,t)))return c;throw new s("Can't convert object to primitive value")}},9152:(t,e,r)=>{"use strict";var n=r(6058),o=r(8844),i=r(2741),s=r(7518),c=r(5027),u=o([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=i.f(c(t)),r=s.f;return r?u(e,r(t)):e}},9302:t=>{"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},7073:(t,e,r)=>{"use strict";var n=r(9037),o=r(7919),i=r(9985),s=r(5266),c=r(6738),u=r(4201),a=r(2532),f=r(8563),p=r(3931),l=r(3615),v=o&&o.prototype,h=u("species"),d=!1,y=i(n.PromiseRejectionEvent),g=s("Promise",(function(){var t=c(o),e=t!==String(o);if(!e&&66===l)return!0;if(p&&(!v.catch||!v.finally))return!0;if(!l||l<51||!/native code/.test(t)){var r=new o((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((r.constructor={})[h]=n,!(d=r.then((function(){}))instanceof n))return!0}return!e&&(a||f)&&!y}));t.exports={CONSTRUCTOR:g,REJECTION_EVENT:y,SUBCLASSING:d}},7919:(t,e,r)=>{"use strict";var n=r(9037);t.exports=n.Promise},2945:(t,e,r)=>{"use strict";var n=r(5027),o=r(8999),i=r(8742);t.exports=function(t,e){if(n(t),o(e)&&e.constructor===t)return e;var r=i.f(t);return(0,r.resolve)(e),r.promise}},562:(t,e,r)=>{"use strict";var n=r(7919),o=r(6431),i=r(7073).CONSTRUCTOR;t.exports=i||!o((function(t){n.all(t).then(void 0,(function(){}))}))},8055:(t,e,r)=>{"use strict";var n=r(2560).f;t.exports=function(t,e,r){r in t||n(t,r,{configurable:!0,get:function(){return e[r]},set:function(t){e[r]=t}})}},4410:t=>{"use strict";var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=e},4684:(t,e,r)=>{"use strict";var n=r(5726),o=TypeError;t.exports=function(t){if(n(t))throw new o("Can't call method on "+t);return t}},4241:(t,e,r)=>{"use strict";var n=r(6058),o=r(2148),i=r(4201),s=r(7697),c=i("species");t.exports=function(t){var e=n(t);s&&e&&!e[c]&&o(e,c,{configurable:!0,get:function(){return this}})}},5997:(t,e,r)=>{"use strict";var n=r(2560).f,o=r(6812),i=r(4201)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!o(t,i)&&n(t,i,{configurable:!0,value:e})}},2713:(t,e,r)=>{"use strict";var n=r(3430),o=r(4630),i=n("keys");t.exports=function(t){return i[t]||(i[t]=o(t))}},4091:(t,e,r)=>{"use strict";var n=r(9037),o=r(5014),i="__core-js_shared__",s=n[i]||o(i,{});t.exports=s},3430:(t,e,r)=>{"use strict";var n=r(3931),o=r(4091);(t.exports=function(t,e){return o[t]||(o[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.34.0",mode:n?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.34.0/LICENSE",source:"https://github.com/zloirock/core-js"})},6373:(t,e,r)=>{"use strict";var n=r(5027),o=r(2655),i=r(5726),s=r(4201)("species");t.exports=function(t,e){var r,c=n(t).constructor;return void 0===c||i(r=n(c)[s])?e:o(r)}},146:(t,e,r)=>{"use strict";var n=r(3615),o=r(3689),i=r(9037).String;t.exports=!!Object.getOwnPropertySymbols&&!o((function(){var t=Symbol("symbol detection");return!i(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},9886:(t,e,r)=>{"use strict";var n,o,i,s,c=r(9037),u=r(1735),a=r(4071),f=r(9985),p=r(6812),l=r(3689),v=r(2688),h=r(6004),d=r(6420),y=r(1500),g=r(4764),m=r(806),b=c.setImmediate,x=c.clearImmediate,w=c.process,S=c.Dispatch,O=c.Function,T=c.MessageChannel,j=c.String,P=0,E={},k="onreadystatechange";l((function(){n=c.location}));var _=function(t){if(p(E,t)){var e=E[t];delete E[t],e()}},A=function(t){return function(){_(t)}},I=function(t){_(t.data)},R=function(t){c.postMessage(j(t),n.protocol+"//"+n.host)};b&&x||(b=function(t){y(arguments.length,1);var e=f(t)?t:O(t),r=h(arguments,1);return E[++P]=function(){u(e,void 0,r)},o(P),P},x=function(t){delete E[t]},m?o=function(t){w.nextTick(A(t))}:S&&S.now?o=function(t){S.now(A(t))}:T&&!g?(s=(i=new T).port2,i.port1.onmessage=I,o=a(s.postMessage,s)):c.addEventListener&&f(c.postMessage)&&!c.importScripts&&n&&"file:"!==n.protocol&&!l(R)?(o=R,c.addEventListener("message",I,!1)):o=k in d("script")?function(t){v.appendChild(d("script"))[k]=function(){v.removeChild(this),_(t)}}:function(t){setTimeout(A(t),0)}),t.exports={set:b,clear:x}},7578:(t,e,r)=>{"use strict";var n=r(8700),o=Math.max,i=Math.min;t.exports=function(t,e){var r=n(t);return r<0?o(r+e,0):i(r,e)}},5290:(t,e,r)=>{"use strict";var n=r(4413),o=r(4684);t.exports=function(t){return n(o(t))}},8700:(t,e,r)=>{"use strict";var n=r(8828);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},3126:(t,e,r)=>{"use strict";var n=r(8700),o=Math.min;t.exports=function(t){return t>0?o(n(t),9007199254740991):0}},690:(t,e,r)=>{"use strict";var n=r(4684),o=Object;t.exports=function(t){return o(n(t))}},8732:(t,e,r)=>{"use strict";var n=r(2615),o=r(8999),i=r(734),s=r(4849),c=r(5899),u=r(4201),a=TypeError,f=u("toPrimitive");t.exports=function(t,e){if(!o(t)||i(t))return t;var r,u=s(t,f);if(u){if(void 0===e&&(e="default"),r=n(u,t,e),!o(r)||i(r))return r;throw new a("Can't convert object to primitive value")}return void 0===e&&(e="number"),c(t,e)}},8360:(t,e,r)=>{"use strict";var n=r(8732),o=r(734);t.exports=function(t){var e=n(t,"string");return o(e)?e:e+""}},3043:(t,e,r)=>{"use strict";var n={};n[r(4201)("toStringTag")]="z",t.exports="[object z]"===String(n)},4327:(t,e,r)=>{"use strict";var n=r(926),o=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return o(t)}},3691:t=>{"use strict";var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},4630:(t,e,r)=>{"use strict";var n=r(8844),o=0,i=Math.random(),s=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+s(++o+i,36)}},9525:(t,e,r)=>{"use strict";var n=r(146);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},5648:(t,e,r)=>{"use strict";var n=r(7697),o=r(3689);t.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},1500:t=>{"use strict";var e=TypeError;t.exports=function(t,r){if(t<r)throw new e("Not enough arguments");return t}},9834:(t,e,r)=>{"use strict";var n=r(9037),o=r(9985),i=n.WeakMap;t.exports=o(i)&&/native code/.test(String(i))},4201:(t,e,r)=>{"use strict";var n=r(9037),o=r(3430),i=r(6812),s=r(4630),c=r(146),u=r(9525),a=n.Symbol,f=o("wks"),p=u?a.for||a:a&&a.withoutSetter||s;t.exports=function(t){return i(f,t)||(f[t]=c&&i(a,t)?a[t]:p("Symbol."+t)),f[t]}},1064:(t,e,r)=>{"use strict";var n=r(6058),o=r(6812),i=r(5773),s=r(3622),c=r(9385),u=r(8758),a=r(8055),f=r(3457),p=r(3841),l=r(2570),v=r(5411),h=r(7697),d=r(3931);t.exports=function(t,e,r,y){var g="stackTraceLimit",m=y?2:1,b=t.split("."),x=b[b.length-1],w=n.apply(null,b);if(w){var S=w.prototype;if(!d&&o(S,"cause")&&delete S.cause,!r)return w;var O=n("Error"),T=e((function(t,e){var r=p(y?e:t,void 0),n=y?new w(t):new w;return void 0!==r&&i(n,"message",r),v(n,T,n.stack,2),this&&s(S,this)&&f(n,this,T),arguments.length>m&&l(n,arguments[m]),n}));if(T.prototype=S,"Error"!==x?c?c(T,O):u(T,O,{name:!0}):h&&g in w&&(a(T,w,g),a(T,w,"prepareStackTrace")),u(T,w),!d)try{S.name!==x&&i(S,"name",x),S.constructor=T}catch(t){}return T}}},752:(t,e,r)=>{"use strict";var n=r(5290),o=r(7370),i=r(9478),s=r(618),c=r(2560).f,u=r(1934),a=r(7807),f=r(3931),p=r(7697),l="Array Iterator",v=s.set,h=s.getterFor(l);t.exports=u(Array,"Array",(function(t,e){v(this,{type:l,target:n(t),index:0,kind:e})}),(function(){var t=h(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=void 0,a(void 0,!0);switch(t.kind){case"keys":return a(r,!1);case"values":return a(e[r],!1)}return a([r,e[r]],!1)}),"values");var d=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!f&&p&&"values"!==d.name)try{c(d,"name",{value:"values"})}catch(t){}},1057:(t,e,r)=>{"use strict";var n=r(9989),o=r(9037),i=r(1735),s=r(1064),c="WebAssembly",u=o[c],a=7!==new Error("e",{cause:7}).cause,f=function(t,e){var r={};r[t]=s(t,e,a),n({global:!0,constructor:!0,arity:1,forced:a},r)},p=function(t,e){if(u&&u[t]){var r={};r[t]=s(c+"."+t,e,a),n({target:c,stat:!0,constructor:!0,arity:1,forced:a},r)}};f("Error",(function(t){return function(e){return i(t,this,arguments)}})),f("EvalError",(function(t){return function(e){return i(t,this,arguments)}})),f("RangeError",(function(t){return function(e){return i(t,this,arguments)}})),f("ReferenceError",(function(t){return function(e){return i(t,this,arguments)}})),f("SyntaxError",(function(t){return function(e){return i(t,this,arguments)}})),f("TypeError",(function(t){return function(e){return i(t,this,arguments)}})),f("URIError",(function(t){return function(e){return i(t,this,arguments)}})),p("CompileError",(function(t){return function(e){return i(t,this,arguments)}})),p("LinkError",(function(t){return function(e){return i(t,this,arguments)}})),p("RuntimeError",(function(t){return function(e){return i(t,this,arguments)}}))},1692:(t,e,r)=>{"use strict";var n=r(9989),o=r(2615),i=r(509),s=r(8742),c=r(9302),u=r(8734);n({target:"Promise",stat:!0,forced:r(562)},{all:function(t){var e=this,r=s.f(e),n=r.resolve,a=r.reject,f=c((function(){var r=i(e.resolve),s=[],c=0,f=1;u(t,(function(t){var i=c++,u=!1;f++,o(r,e,t).then((function(t){u||(u=!0,s[i]=t,--f||n(s))}),a)})),--f||n(s)}));return f.error&&a(f.value),r.promise}})},5089:(t,e,r)=>{"use strict";var n=r(9989),o=r(3931),i=r(7073).CONSTRUCTOR,s=r(7919),c=r(6058),u=r(9985),a=r(1880),f=s&&s.prototype;if(n({target:"Promise",proto:!0,forced:i,real:!0},{catch:function(t){return this.then(void 0,t)}}),!o&&u(s)){var p=c("Promise").prototype.catch;f.catch!==p&&a(f,"catch",p,{unsafe:!0})}},6697:(t,e,r)=>{"use strict";var n,o,i,s=r(9989),c=r(3931),u=r(806),a=r(9037),f=r(2615),p=r(1880),l=r(9385),v=r(5997),h=r(4241),d=r(509),y=r(9985),g=r(8999),m=r(767),b=r(6373),x=r(9886).set,w=r(231),S=r(920),O=r(9302),T=r(4410),j=r(618),P=r(7919),E=r(7073),k=r(8742),_="Promise",A=E.CONSTRUCTOR,I=E.REJECTION_EVENT,R=E.SUBCLASSING,C=j.getterFor(_),L=j.set,D=P&&P.prototype,M=P,N=D,F=a.TypeError,G=a.document,U=a.process,B=k.f,V=B,W=!!(G&&G.createEvent&&a.dispatchEvent),z="unhandledrejection",H=function(t){var e;return!(!g(t)||!y(e=t.then))&&e},K=function(t,e){var r,n,o,i=e.value,s=1===e.state,c=s?t.ok:t.fail,u=t.resolve,a=t.reject,p=t.domain;try{c?(s||(2===e.rejection&&X(e),e.rejection=1),!0===c?r=i:(p&&p.enter(),r=c(i),p&&(p.exit(),o=!0)),r===t.promise?a(new F("Promise-chain cycle")):(n=H(r))?f(n,r,u,a):u(r)):a(i)}catch(t){p&&!o&&p.exit(),a(t)}},q=function(t,e){t.notified||(t.notified=!0,w((function(){for(var r,n=t.reactions;r=n.get();)K(r,t);t.notified=!1,e&&!t.rejection&&Y(t)})))},J=function(t,e,r){var n,o;W?((n=G.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),a.dispatchEvent(n)):n={promise:e,reason:r},!I&&(o=a["on"+t])?o(n):t===z&&S("Unhandled promise rejection",r)},Y=function(t){f(x,a,(function(){var e,r=t.facade,n=t.value;if(Q(t)&&(e=O((function(){u?U.emit("unhandledRejection",n,r):J(z,r,n)})),t.rejection=u||Q(t)?2:1,e.error))throw e.value}))},Q=function(t){return 1!==t.rejection&&!t.parent},X=function(t){f(x,a,(function(){var e=t.facade;u?U.emit("rejectionHandled",e):J("rejectionhandled",e,t.value)}))},$=function(t,e,r){return function(n){t(e,n,r)}},Z=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,q(t,!0))},tt=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new F("Promise can't be resolved itself");var n=H(e);n?w((function(){var r={done:!1};try{f(n,e,$(tt,r,t),$(Z,r,t))}catch(e){Z(r,e,t)}})):(t.value=e,t.state=1,q(t,!1))}catch(e){Z({done:!1},e,t)}}};if(A&&(N=(M=function(t){m(this,N),d(t),f(n,this);var e=C(this);try{t($(tt,e),$(Z,e))}catch(t){Z(e,t)}}).prototype,(n=function(t){L(this,{type:_,done:!1,notified:!1,parent:!1,reactions:new T,rejection:!1,state:0,value:void 0})}).prototype=p(N,"then",(function(t,e){var r=C(this),n=B(b(this,M));return r.parent=!0,n.ok=!y(t)||t,n.fail=y(e)&&e,n.domain=u?U.domain:void 0,0===r.state?r.reactions.add(n):w((function(){K(n,r)})),n.promise})),o=function(){var t=new n,e=C(t);this.promise=t,this.resolve=$(tt,e),this.reject=$(Z,e)},k.f=B=function(t){return t===M||void 0===t?new o(t):V(t)},!c&&y(P)&&D!==Object.prototype)){i=D.then,R||p(D,"then",(function(t,e){var r=this;return new M((function(t,e){f(i,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete D.constructor}catch(t){}l&&l(D,N)}s({global:!0,constructor:!0,wrap:!0,forced:A},{Promise:M}),v(M,_,!1,!0),h(_)},3964:(t,e,r)=>{"use strict";r(6697),r(1692),r(5089),r(8829),r(2092),r(7905)},8829:(t,e,r)=>{"use strict";var n=r(9989),o=r(2615),i=r(509),s=r(8742),c=r(9302),u=r(8734);n({target:"Promise",stat:!0,forced:r(562)},{race:function(t){var e=this,r=s.f(e),n=r.reject,a=c((function(){var s=i(e.resolve);u(t,(function(t){o(s,e,t).then(r.resolve,n)}))}));return a.error&&n(a.value),r.promise}})},2092:(t,e,r)=>{"use strict";var n=r(9989),o=r(2615),i=r(8742);n({target:"Promise",stat:!0,forced:r(7073).CONSTRUCTOR},{reject:function(t){var e=i.f(this);return o(e.reject,void 0,t),e.promise}})},7905:(t,e,r)=>{"use strict";var n=r(9989),o=r(6058),i=r(3931),s=r(7919),c=r(7073).CONSTRUCTOR,u=r(2945),a=o("Promise"),f=i&&!c;n({target:"Promise",stat:!0,forced:i||c},{resolve:function(t){return u(f&&this===a?s:this,t)}})},6265:(t,e,r)=>{"use strict";var n=r(9037),o=r(6338),i=r(3265),s=r(752),c=r(5773),u=r(5997),a=r(4201)("iterator"),f=s.values,p=function(t,e){if(t){if(t[a]!==f)try{c(t,a,f)}catch(e){t[a]=f}if(u(t,e,!0),o[e])for(var r in s)if(t[r]!==s[r])try{c(t,r,s[r])}catch(e){t[r]=s[r]}}};for(var l in o)p(n[l]&&n[l].prototype,l);p(i,"DOMTokenList")}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n].call(i.exports,i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};return(()=>{"use strict";r.r(n),r.d(n,{AudioExtension:()=>P,AudioProcessor:()=>T,CreateAutoAdjuster:()=>R,Extension:()=>j,PromiseMutex:()=>M,Ticker:()=>L,VideoProcessor:()=>O,logger:()=>y,reporter:()=>b});var t=r(1685),e=r(1768),o=r(6228);function i(t){return i="function"==typeof e&&"symbol"==typeof o?function(t){return typeof t}:function(t){return t&&"function"==typeof e&&t.constructor===e&&t!==e.prototype?"symbol":typeof t},i(t)}var s=r(4360);function c(e,r,n){return(r=function(t){var e=function(t,e){if("object"!==i(t)||null===t)return t;var r=t[s];if(void 0!==r){var n=r.call(t,"string");if("object"!==i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===i(e)?e:String(e)}(r))in e?t(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}r(1057),r(752),r(6265);var u=r(6226),a=r.n(u);class f{constructor(){c(this,"_events",{}),c(this,"addListener",this.on)}getListeners(t){return this._events[t]?this._events[t].map((t=>t.listener)):[]}on(t,e){this._events[t]||(this._events[t]=[]);const r=this._events[t];-1===this._indexOfListener(r,e)&&r.push({listener:e,once:!1})}once(t,e){this._events[t]||(this._events[t]=[]);const r=this._events[t];-1===this._indexOfListener(r,e)&&r.push({listener:e,once:!0})}off(t,e){if(!this._events[t])return;const r=this._events[t],n=this._indexOfListener(r,e);-1!==n&&r.splice(n,1),0===this._events[t].length&&delete this._events[t]}removeAllListeners(t){t?delete this._events[t]:this._events={}}emit(t){this._events[t]||(this._events[t]=[]);const e=this._events[t].map((t=>t));for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];for(let r=0;r<e.length;r+=1){const o=e[r];o.once&&this.off(t,o.listener),o.listener.apply(this,n||[])}}_indexOfListener(t,e){let r=t.length;for(;r--;)if(t[r].listener===e)return r;return-1}emitAsPromise(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return 0===this.getListeners(t).length?a().reject(new Error("No promise event handler.")):new(a())(((e,n)=>{this.emit(t,...r,e,n)}))}emitAsPromiseNoResponse(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];return 0===this.getListeners(t).length?a().resolve():new(a())(((e,n)=>{this.emit(t,...r,e,n)}))}}function p(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:7,e=arguments.length>1?arguments[1]:void 0;const r=Math.random().toString(16).substr(2,t).toLowerCase();return r.length===t?"".concat(e).concat(r):"".concat(e).concat(r)+p(t-r.length,"")}const l=Date.now(),v={DEBUG:0,INFO:1,WARNING:2,ERROR:3,NONE:4};function h(){const t=new Date;return t.toTimeString().split(" ")[0]+":"+t.getMilliseconds()}const d=t=>{for(const e in v)if(Object.prototype.hasOwnProperty.call(v,e)&&v[e]===t)return e;return"DEFAULT"},y=new class{constructor(){c(this,"logLevel",v.DEBUG),c(this,"hookLog",void 0)}setLogLevel(t){t=Math.min(Math.max(0,t),4),this.logLevel=t}debug(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n=[v.DEBUG].concat(e);this.log.apply(this,n)}info(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n=[v.INFO].concat(e);this.log.apply(this,n)}warning(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n=[v.WARNING].concat(e);this.log.apply(this,n)}error(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];const n=[v.ERROR].concat(e);this.log.apply(this,n)}log(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];if(Date.now()-l<100)return void setTimeout((()=>{this.log(...r)}),Date.now()-l);const o=Math.max(0,Math.min(4,r[0]));if(r[0]=h()+" Agora-Extension [".concat(d(o),"]:"),null===(t=this.hookLog)||void 0===t||t.call(this,o,r),o<this.logLevel)return;const i=h()+" %cAgora-Extension [".concat(d(o),"]:");let s=[];switch(o){case v.DEBUG:s=[i,"color: #64B5F6;"].concat(r.slice(1)),console.log.apply(console,s);break;case v.INFO:s=[i,"color: #1E88E5; font-weight: bold;"].concat(r.slice(1)),console.log.apply(console,s);break;case v.WARNING:s=[i,"color: #FB8C00; font-weight: bold;"].concat(r.slice(1)),console.warn.apply(console,s);break;case v.ERROR:s=[i,"color: #B00020; font-weight: bold;"].concat(r.slice(1)),console.error.apply(console,s)}}};function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){c(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}const b=new class{constructor(){c(this,"apiInvokeMsgQueue",[]),c(this,"hookApiInvoke",void 0)}reportApiInvoke(t){t.timeout=t.timeout||6e4,t.reportResult=void 0===t.reportResult||t.reportResult;const e=Date.now(),r=()=>({name:t.name,apiInvokeTime:e,options:t.options});let n=!1;var o;(o=t.timeout,new(a())((t=>{window.setTimeout(t,o)}))).then((()=>{n||(this.sendApiInvoke(m(m({},r()),{},{error:"API_INVOKE_TIMEOUT",success:!1})),y.debug("".concat(t.name," timeout")))}));const i=new Error("".concat(t.name,": this api invoke is end"));return{onSuccess:e=>{if(n)throw i;n=!0,this.sendApiInvoke(m(m({},r()),{},{success:!0},t.reportResult&&{result:e}))},onError:t=>{if(n)throw t;n=!0,this.sendApiInvoke(m(m({},r()),{},{success:!1,error:t.toString()}))}}}sendApiInvoke(t){this.hookApiInvoke?(this.hookApiInvoke([...this.apiInvokeMsgQueue,t]),this.apiInvokeMsgQueue=[]):this.apiInvokeMsgQueue.push(t)}};function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach((function(e){c(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class S extends f{get enabled(){return this._enabled}output(t,e){if(this.outputTrack===t)return;const r=b.reportApiInvoke({name:"".concat(this.name,".output"),options:{track:null==t?void 0:t.toString()}});this.outputTrack=t,this.destination&&this.destination.updateInput({track:t,context:e}),r.onSuccess()}constructor(){super(),c(this,"inputTrack",void 0),c(this,"outputTrack",void 0),c(this,"_enabled",!0),c(this,"_source",void 0),c(this,"ID",p(6,"")),c(this,"destination",void 0),c(this,"context",void 0),c(this,"name",void 0)}enable(){if(this._enabled)return;const t=b.reportApiInvoke({name:"".concat(this.name,".enable"),options:!0});y.info("".concat(this.name,"-").concat(this.ID," enabled")),this._enabled=!0;try{var e;const r=null===(e=this.onEnableChange)||void 0===e?void 0:e.call(this,this._enabled);if(r instanceof a())return r.then((()=>{t.onSuccess()})).catch((e=>{throw t.onError(e),e}));t.onSuccess()}catch(e){throw t.onError(e),e}}disable(){if(!this._enabled)return;const t=b.reportApiInvoke({name:"".concat(this.name,".enable"),options:!1});y.info("".concat(this.name,"-").concat(this.ID," disabled")),this._enabled=!1;try{var e;const r=null===(e=this.onEnableChange)||void 0===e?void 0:e.call(this,this._enabled);if(r instanceof a())return r.then((()=>{t.onSuccess()})).catch((e=>{throw t.onError(e),e}));t.onSuccess()}catch(e){throw t.onError(e),e}}}class O extends S{get kind(){return"video"}pipe(t){const e=b.reportApiInvoke({name:"".concat(this.name,".pipe"),options:{processor:t.name}});if(this.destination===t)return e.onSuccess(),t;if(t._source){const r=new Error("Processor ".concat(t.name," already being piped, please call ").concat(t._source.name,".unpipe() beforehand."));throw e.onError(r),r}return this.destination&&this.unpipe(),this.destination=t,t._source=this,this.context&&this.destination.updateInput({track:this.outputTrack,context:this.context}),e.onSuccess(),t}unpipe(){if(!this.destination)return;const t=b.reportApiInvoke({name:"".concat(this.name,".unpipe"),options:{processor:this.destination.name}});y.info("unpiping processor ".concat(this.destination.name,"-").concat(this.destination.ID));try{const t=this.destination;this.destination._source=void 0,this.destination=void 0,t.reset()}finally{t.onSuccess()}}updateInput(t){var e,r;t.context!==this.context&&(this.context=t.context,null===(e=this.onPiped)||void 0===e||e.call(this,this.context),this.destination&&this.destination.updateInput({track:this.outputTrack,context:t.context})),t.track&&this.inputTrack!==t.track&&(this.inputTrack=t.track,null===(r=this.onTrack)||void 0===r||r.call(this,t.track,this.context))}reset(){var t;this.context&&this.context.requestRevertConstraints(this),this.inputTrack=void 0,this.context=void 0,null===(t=this.onUnpiped)||void 0===t||t.call(this),this.destination&&this.destination.reset()}}class T extends S{constructor(){super(...arguments),c(this,"inputNode",void 0),c(this,"outputNode",void 0),c(this,"destination",void 0),c(this,"context",void 0)}get kind(){return"audio"}pipe(t){const e=b.reportApiInvoke({name:"".concat(this.name,".pipe"),options:{processor:t.name}});if(this.destination===t)return e.onSuccess(),t;if(t._source){const r=new Error("Processor ".concat(t.name," already being piped, please call ").concat(t._source.name,".unpipe() beforehand."));throw e.onError(r),r}return this.destination&&this.unpipe(),this.destination=t,t._source=this,this.context&&this.destination.updateInput({track:this.outputTrack,node:this.outputNode,context:this.context}),e.onSuccess(),t}unpipe(){if(!this.destination)return;y.info("unpiping processor ".concat(this.destination.name,"-").concat(this.destination.ID));const t=b.reportApiInvoke({name:"".concat(this.name,".unpipe"),options:{processor:this.destination.name}});try{var e;let t=this.destination;null===(e=this.outputNode)||void 0===e||e.disconnect(),this.destination._source=void 0,this.destination=void 0,t.reset()}finally{t.onSuccess()}}output(t,e){if(t instanceof MediaStreamTrack)super.output(t,e);else if(t instanceof AudioNode){if(this.outputNode===t)return;const r=b.reportApiInvoke({name:"".concat(this.name,".output"),options:w(w({},t instanceof MediaStreamTrack&&{track:t.toString()}),t instanceof AudioNode&&{audioNode:t.toString()})});this.outputNode=t,this.destination&&this.destination.updateInput({node:t,context:e}),r.onSuccess()}}updateInput(t){var e,r,n;t.context!==this.context&&(this.context=t.context,null===(e=this.onPiped)||void 0===e||e.call(this,this.context),this.destination&&this.destination.updateInput({track:this.outputTrack,node:this.outputNode,context:t.context})),t.track&&this.inputTrack!==t.track&&(this.inputTrack=t.track,null===(r=this.onTrack)||void 0===r||r.call(this,t.track,this.context)),t.node&&this.inputNode!==t.node&&(this.inputNode=t.node,null===(n=this.onNode)||void 0===n||n.call(this,t.node,this.context))}reset(){var t;this.context&&this.context.requestRevertConstraints(this),this.inputTrack=void 0,this.inputNode=void 0,null===(t=this.onUnpiped)||void 0===t||t.call(this,this.context),this.context=void 0,this.destination&&this.destination.reset()}}class j{constructor(){c(this,"__registered__",!1),c(this,"logger",y),c(this,"reporter",b),c(this,"parameters",{})}createProcessor(){if(!this.__registered__)throw new Error("Extension not registered yet!");return this._createProcessor()}static setLogLevel(t){y.setLogLevel(t)}}class P extends j{}r(3964);class E{constructor(t){c(this,"config",null),c(this,"procecssor",void 0),c(this,"frameDropped",[]),c(this,"mstId",null),c(this,"direction","local"),c(this,"presiousFramesDropped",0),c(this,"targetFramerate",0),c(this,"checkTimes",5),this.procecssor=t}async onProcessFrame(t,e){return!!await this.shouldDisableProcessor(t,e)&&(await this.disableProcessor(),!0)}getTrackFramerate(){return this.targetFramerate}setMediaStreamTrackInfo(t,e){this.targetFramerate=e||0}async disableProcessor(){console.warn("agora-extension-super-clarity is disabled automatically due to performance bottleneck"),await this.procecssor.disable()}}class k extends E{constructor(t,e){super(t),c(this,"counter",0),c(this,"totalProcessTime",0),c(this,"presiousCheckPoint",-1),this.config=e}setMediaStreamTrackInfo(t,e){super.setMediaStreamTrackInfo(t,e)}setConfig(t){var e;null!==(e=this.config)&&void 0!==e&&e.targetAverageTime&&t.targetAverageTime&&Math.abs(this.config.targetAverageTime-t.targetAverageTime)/this.config.targetAverageTime>.25&&(this.counter=0,this.totalProcessTime=0,this.presiousCheckPoint=-1),this.config=t}reset(){this.counter=0,this.presiousCheckPoint=-1,this.totalProcessTime=0}async shouldDisableProcessor(t,e){var r,n;if(-1===this.presiousCheckPoint)return this.presiousCheckPoint=t,this.totalProcessTime=e-t,this.counter=1,!1;const o=(null===(r=this.config)||void 0===r?void 0:r.checkDuration)||1e3;if(e-this.presiousCheckPoint<o)return this.totalProcessTime+=e-t,this.counter++,!1;let i=!1;return null!==(n=this.config)&&void 0!==n&&n.targetAverageTime&&this.totalProcessTime/this.counter>=this.config.targetAverageTime&&(i=!0),this.presiousCheckPoint=e,this.totalProcessTime=0,this.counter=0,i}}class _ extends E{constructor(t,e){super(t),c(this,"counter",0),c(this,"presiousCheckPoint",-1),c(this,"framerate",[]),this.config=e}setMediaStreamTrackInfo(t,e){super.setMediaStreamTrackInfo(t,e)}setConfig(t){var e;null!==(e=this.config)&&void 0!==e&&e.targetFps&&t.targetFps&&Math.abs(this.config.targetFps-t.targetFps)/this.config.targetFps>.25&&(this.counter=0,this.presiousCheckPoint=-1,this.framerate=[]),this.config=t}reset(){this.counter=0,this.presiousCheckPoint=-1,this.framerate=[]}}class A extends _{constructor(){super(...arguments),c(this,"videoFrameRate",0)}setMediaStreamTrackInfo(t,e){if(e)this.videoFrameRate=e;else{const t="invalid framerate";console.warn(t),y.warning(t),this.videoFrameRate=15}super.setMediaStreamTrackInfo(t,e)}async shouldDisableProcessor(t,e){var r;t-this.presiousCheckPoint>1e3&&(this.presiousCheckPoint>-1&&this.framerate.push(this.counter),this.presiousCheckPoint=t,this.counter=0),this.counter++;let n=!0;const o=this.framerate.length;if(this.framerate.length==((null===(r=this.config)||void 0===r?void 0:r.checkTimes)||5)){for(let t=0;t<o;t++)if(this.framerate[t]>.9*this.videoFrameRate){n=!1;break}this.framerate=[]}else n=!1;return n}}class I extends _{constructor(t,e){super(t,e),c(this,"receiver",null)}setMediaStreamTrackInfo(t,e){if(this.mstId=t,super.setMediaStreamTrackInfo(t,e),""===t)return;const r=__ARTC__.__CLIENT_LIST__.length;for(let e=0;e<r;e++){let r=__ARTC__.__CLIENT_LIST__[e]._p2pChannel.connection.peerConnection.getReceivers();for(let e=0;e<r.length;e++)if(t===r[e].track.id)return void(this.receiver=r[e])}}async shouldDisableProcessor(t,e){var r;if(t-this.presiousCheckPoint>1e3){if(!this.receiver)return!1;let e=0,r=0;(await this.receiver.getStats()).forEach((t=>{"inbound-rtp"===t.type&&"video"===t.kind&&(e=t.framesDropped,r=t.framesPerSecond)})),this.presiousCheckPoint>-1&&this.framerate.push(this.counter),this.presiousCheckPoint=t,this.counter=0,this.frameDropped.push(e-this.presiousFramesDropped),this.presiousFramesDropped=e,this.config&&Math.abs(this.config.targetFps-r)/this.config.targetFps>.25&&(this.config.targetFps=r)}this.counter++;let n=!0;const o=this.framerate.length;if(this.framerate.length==((null===(r=this.config)||void 0===r?void 0:r.checkTimes)||5)){for(let t=0;t<o;t++){var i;if(null!==(i=this.config)&&void 0!==i&&i.targetFps&&this.framerate[t]>.9*this.config.targetFps){n=!1;break}}if(!n){n=!0;for(let t=0;t<o;t++)if(0===this.frameDropped[t]){n=!1;break}}this.framerate=[],this.frameDropped=[]}else n=!1;return n}}function R(t,e,r,n){if("cpu"===t.toLocaleLowerCase())return new k(r,n);if("gpu"===t.toLocaleLowerCase())return"local"===e.toLocaleLowerCase()?new A(r,n):new I(r,n);throw new Error("ProcessorBackend is not supported!")}let C;class L{get running(){return this._running}constructor(t,e){if(c(this,"type",void 0),c(this,"interval",void 0),c(this,"fn",void 0),c(this,"_running",!1),c(this,"_osc",void 0),!t)throw new Error;if(e<=0)throw new Error;this.type=t,this.interval=e}add(t){this.fn=t}remove(){this.fn=void 0}start(){if(!this._running)switch(this._running=!0,this.type){case"Timer":{const t=()=>{setTimeout((()=>{this.fn&&this.fn(),this._running&&t()}),this.interval)};t();break}case"RAF":{const t=e=>{requestAnimationFrame((r=>{r-e<this.interval?this._running&&t(e):(this.fn&&this.fn(),this._running&&t(r))}))};t(performance.now());break}case"Oscillator":{C||(C=new AudioContext);const t=C.createGain();let e;t.gain.value=0,t.connect(C.destination);const r=()=>{this.fn&&this.fn(),e&&e.disconnect(),e=C.createOscillator(),this._osc=e,e.connect(t),this._running&&(e.onended=r,e.start(0),e.stop(C.currentTime+this.interval/1e3))};r();break}}}stop(){this._running=!1,this._osc&&(this._osc.onended=null,this._osc=void 0)}}let D=1;class M{constructor(t){c(this,"lockingPromise",a().resolve()),c(this,"locks",0),c(this,"name",""),c(this,"lockId",void 0),this.lockId=D++,t&&(this.name=t)}get isLocked(){return this.locks>0}lock(t){let e;this.locks+=1;const r=new(a())((t=>{e=()=>{this.locks-=1,t()}})),n=this.lockingPromise.then((()=>e));return this.lockingPromise=this.lockingPromise.then((()=>r)),n}}})(),n})()));