# v1.1.0
- Add public abstract optional method `Extension.checkCompatibility`  to provide a unified interface to help developers check browser compatibility. 
# v1.2.3
- Added `IProcessorContext.gatherUsage` method to gather extension usage information.
- Fixed some logging typos.
# v1.2.4
- Added `IExtensionLogger.setLogLevel` method to set the output log level of the extension.
- Add public static method `Extension.setLogLevel`  to set the output log level of the extension.